# 项目gitignore配置文件
# 排除不需要提交到版本控制的文件和文件夹

# ===== Augment配置文件 =====
.augmentignore

# ===== 编译产物和构建文件 =====
# React前端构建产物
qianduan/build/
qianduan/dist/

# Rust后端编译产物
houduan/target/

# ===== 依赖包文件夹 =====
# Node.js依赖包
qianduan/node_modules/

# ===== IDE配置文件 =====
# IntelliJ IDEA配置
.idea/
houduan/.idea/

# Visual Studio Code配置
.vscode/

# ===== 锁文件（可选择性提交） =====
# 注意：通常package-lock.json和Cargo.lock应该提交
# 但如果你不想提交锁文件，可以取消下面的注释
# qianduan/package-lock.json
# houduan/Cargo.lock

# yarn锁文件
qianduan/yarn.lock

# ===== 日志文件 =====
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== 临时文件和缓存 =====
# 操作系统临时文件
.DS_Store
Thumbs.db

# 编辑器临时文件
*~
*.swp
*.swo

# 测试覆盖率报告
qianduan/coverage/

# ===== 环境配置文件 =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== 其他不需要的文件 =====
# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 运行时文件
*.pid
*.seed
*.pid.lock

# 调试文件
.nyc_output/

# 依赖目录
jspm_packages/

# TypeScript缓存
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
