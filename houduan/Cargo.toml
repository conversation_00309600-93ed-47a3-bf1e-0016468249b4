[package]
name = "houduan"
version = "0.1.0"
edition = "2021"

[dependencies]
aes = "0.8.4"
aes-gcm = "0.10.3"
anyhow = "1.0.98"
base64 = "0.22.1"
bincode = "1.3.3"
bcrypt = "0.17.0"
chrono = { version = "0.4.41", features = ["serde"] }
config = "0.15.13"
dotenv = "0.15.0"
elliptic-curve = { version = "0.13.8", features = ["ecdh"] }
env_logger = "0.11.8"
hex = "0.4.3"
log = "0.4.27"
p256 = { version = "0.13.2", features = ["ecdh", "serde"] }
rand = "0.9.2"
rocket = { version = "0.5.1", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"
serde_yaml = "0.9.34-deprecated"
sha2 = "0.10.9"
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "mysql", "chrono", "uuid"] }
thiserror = "2.0.12"
tokio = { version = "1.47.0", features = ["full"] }
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
uuid = { version = "1.17.0", features = ["v4", "serde"] }
redis = { version = "0.32.4", features = ["tokio-comp"] }

deadpool-redis = "0.22.0"
