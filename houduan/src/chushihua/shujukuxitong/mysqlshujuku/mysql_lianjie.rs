#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::peizhi::zongpeizhi_peizhi::peizhixitong_shujuku_peizhi;
use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::shujukuxitong_rizhi;
use sqlx::{MySqlPool, MySqlConnection, Row};
use sqlx::mysql::MySqlPoolOptions;

/// mysql连接管理器
#[derive(Clone)]
pub struct mysql_lianjie_guanli {
    pub lianjiechi: Option<MySqlPool>,
}

impl Default for mysql_lianjie_guanli {
    fn default() -> Self {
        Self::new()
    }
}

impl mysql_lianjie_guanli {
    /// 创建新的mysql连接管理器实例
    pub fn new() -> Self {
        Self {
            lianjiechi: None
        }
    }

    /// 连接到mysql数据库并创建连接池
    pub async fn lianjie(&mut self, peizhi: &peizhixitong_shujuku_peizhi) -> anyhow::Result<()> {
        // 首先连接到mysql服务器（不指定数据库）
        let fuwuqi_url = format!(
            "mysql://{}:{}@{}:{}",
            peizhi.yonghu,
            peizhi.mima,
            peizhi.shujukuip,
            peizhi.duankou
        );

        // 创建临时连接池用于检查和创建数据库
        let linshi_lianjiechi = match MySqlPoolOptions::new()
            .max_connections(1)
            .connect(&fuwuqi_url)
            .await
        {
            Ok(pool) => pool,
            Err(e) => {
                shujukuxitong_rizhi("mysql", "创建临时连接池失败");
                return Err(e.into());
            }
        };

        // 检查数据库是否存在
        let jiancha_sql = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'xianjingziliaozhan'";
        let jieguo = match sqlx::query(jiancha_sql)
            .fetch_optional(&linshi_lianjiechi)
            .await
        {
            Ok(result) => result,
            Err(e) => {
                shujukuxitong_rizhi("mysql", "检查数据库是否存在失败");
                return Err(e.into());
            }
        };

        if jieguo.is_none() {
            // 数据库不存在，创建数据库
            let chuangjian_sql = "CREATE DATABASE xianjingziliaozhan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if let Err(e) = sqlx::query(chuangjian_sql)
                .execute(&linshi_lianjiechi)
                .await
            {
                shujukuxitong_rizhi("mysql", "创建数据库失败");
                return Err(e.into());
            }

            shujukuxitong_rizhi("mysql", "数据库xianjingziliaozhan不存在，已自动创建！");
        } else {
            shujukuxitong_rizhi("mysql", "数据库存在，正在执行长连接！");
        }

        // 关闭临时连接池
        linshi_lianjiechi.close().await;

        // 现在连接到指定的数据库
        let shujuku_url = format!(
            "mysql://{}:{}@{}:{}/xianjingziliaozhan",
            peizhi.yonghu,
            peizhi.mima,
            peizhi.shujukuip,
            peizhi.duankou
        );

        // 创建连接池配置
        let lianjiechi = match MySqlPoolOptions::new()
            .max_connections(20)           // 最大连接数
            .min_connections(5)            // 最小连接数
            .acquire_timeout(std::time::Duration::from_secs(30))  // 获取连接超时时间
            .idle_timeout(std::time::Duration::from_secs(600))    // 空闲连接超时时间(10分钟)
            .max_lifetime(std::time::Duration::from_secs(1800))   // 连接最大生存时间(30分钟)
            .connect(&shujuku_url)
            .await
        {
            Ok(pool) => pool,
            Err(e) => {
                shujukuxitong_rizhi("mysql", "创建数据库连接池失败");
                return Err(e.into());
            }
        };

        self.lianjiechi = Some(lianjiechi);
        Ok(())
    }

    /// 从连接池获取连接
    pub async fn huoqu_lianjie(&self) -> anyhow::Result<sqlx::pool::PoolConnection<sqlx::MySql>> {
        match &self.lianjiechi {
            Some(pool) => match pool.acquire().await {
                Ok(conn) => Ok(conn),
                Err(e) => {
                    shujukuxitong_rizhi("mysql", "获取连接失败");
                    Err(e.into())
                }
            },
            None => {
                let err_msg = "mysql连接池未初始化";
                shujukuxitong_rizhi("mysql", "获取连接失败");
                Err(anyhow::anyhow!(err_msg))
            }
        }
    }

    /// 获取连接池引用（用于直接执行查询）
    pub fn huoqu_lianjiechi(&self) -> anyhow::Result<&MySqlPool> {
        match &self.lianjiechi {
            Some(pool) => Ok(pool),
            None => {
                let err_msg = "mysql连接池未初始化";
                shujukuxitong_rizhi("mysql", "获取连接池失败");
                Err(anyhow::anyhow!(err_msg))
            }
        }
    }

    /// 执行查询并返回结果
    pub async fn zhixing_chaxun(&self, sql: &str) -> anyhow::Result<Vec<sqlx::mysql::MySqlRow>> {
        let pool = self.huoqu_lianjiechi()?;
        let jieguo = sqlx::query(sql)
            .fetch_all(pool)
            .await?;
        Ok(jieguo)
    }

    /// 执行更新操作（INSERT, UPDATE, DELETE）
    pub async fn zhixing_gengxin(&self, sql: &str) -> anyhow::Result<u64> {
        let pool = self.huoqu_lianjiechi()?;
        let jieguo = sqlx::query(sql)
            .execute(pool)
            .await?;
        Ok(jieguo.rows_affected())
    }

    /// 执行带参数的查询
    pub async fn zhixing_chaxun_with_canshu<T>(&self, sql: &str, canshu: T) -> anyhow::Result<Vec<sqlx::mysql::MySqlRow>>
    where
        T: Send + for<'q> sqlx::IntoArguments<'q, sqlx::MySql>,
    {
        let pool = self.huoqu_lianjiechi()?;
        let jieguo = sqlx::query_with(sql, canshu)
            .fetch_all(pool)
            .await?;
        Ok(jieguo)
    }

    /// 执行带参数的更新操作
    pub async fn zhixing_gengxin_with_canshu<T>(&self, sql: &str, canshu: T) -> anyhow::Result<u64>
    where
        T: Send + for<'q> sqlx::IntoArguments<'q, sqlx::MySql>,
    {
        let pool = self.huoqu_lianjiechi()?;
        let jieguo = sqlx::query_with(sql, canshu)
            .execute(pool)
            .await?;
        Ok(jieguo.rows_affected())
    }

    /// 开始事务
    pub async fn kaishi_shiwu(&self) -> anyhow::Result<sqlx::Transaction<'_, sqlx::MySql>> {
        let pool = self.huoqu_lianjiechi()?;
        let shiwu = pool.begin().await?;
        Ok(shiwu)
    }

    /// 检查连接池状态
    pub fn jiancha_zhuangtai(&self) -> anyhow::Result<String> {
        match &self.lianjiechi {
            Some(pool) => {
                let zhuangtai = format!(
                    "mysql连接池状态 - 活跃连接: {}, 空闲连接: {}, 总连接: {}",
                    pool.num_idle(),
                    pool.num_idle(),
                    pool.size()
                );
                Ok(zhuangtai)
            }
            None => Err(anyhow::anyhow!("mysql连接池未初始化"))
        }
    }

    /// 测试数据库连接
    pub async fn ceshi_lianjie(&self) -> anyhow::Result<()> {
        let pool = self.huoqu_lianjiechi()?;
        if let Err(e) = sqlx::query("SELECT 1").fetch_one(pool).await {
            shujukuxitong_rizhi("mysql", "数据库连接测试失败");
            return Err(e.into());
        }
        Ok(())
    }

    /// 测试长连接功能
    /// 执行多次查询，验证连接池是否正常复用连接
    pub async fn ceshi_changlianjie(&self) -> anyhow::Result<()> {
        let pool = self.huoqu_lianjiechi()?;

        // 执行多次查询来测试连接复用
        for i in 1..=5 {
            let sql = format!("SELECT {} as ceshi_shu, CONNECTION_ID() as lianjie_id, NOW() as shijian", i);
            let jieguo = sqlx::query(&sql)
                .fetch_one(pool)
                .await?;

            let ceshi_shu: i32 = jieguo.get("ceshi_shu");
            let lianjie_id: u32 = jieguo.get("lianjie_id");
            let shijian: chrono::NaiveDateTime = jieguo.get("shijian");

            println!("测试 {} - 连接ID: {}, 时间: {}", ceshi_shu, lianjie_id, shijian);

            // 短暂延迟
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        Ok(())
    }
}
