#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::shujukuxitong_rizhi;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_shujuku_guanli::{mysql_shujuku_guanli, biao_dingyi, ziduan_dingyi, suoyin_dingyi};

/// 用户数据库表定义和管理
pub struct yonghu_shujuku_biao;

impl yonghu_shujuku_biao {
    /// 获取用户表名
    pub fn huoqu_biao_ming() -> &'static str {
        "yonghu"
    }

    /// 获取用户表的完整定义
    pub fn huoqu_biao_dingyi() -> biao_dingyi {
        biao_dingyi {
            biao_ming: Self::huoqu_biao_ming().to_string(),
            ziduan_liebiao: Self::huoqu_ziduan_dingyi(),
            suoyin_liebiao: Self::huoqu_suoyin_dingyi(),
            zhushi: Some("用户信息表".to_string()),
        }
    }

    /// 获取用户表的字段定义
    fn huoqu_ziduan_dingyi() -> Vec<ziduan_dingyi> {
        vec![
            // id - 账号唯一ID，主键，自增
            ziduan_dingyi {
                ziduan_ming: "id".to_string(),
                shujuleixing: "BIGINT UNSIGNED".to_string(),
                moren_zhi: None,
                buneng_weikong: true,
                zhujian: true,
                zizeng: true,
                zhushi: Some("账号唯一ID".to_string()),
            },
            
            // zh - 账号字段
            ziduan_dingyi {
                ziduan_ming: "zh".to_string(),
                shujuleixing: "VARCHAR(50)".to_string(),
                moren_zhi: None,
                buneng_weikong: true,
                zhujian: false,
                zizeng: false,
                zhushi: Some("账号".to_string()),
            },
            
            // mm - 密码字段
            ziduan_dingyi {
                ziduan_ming: "mm".to_string(),
                shujuleixing: "VARCHAR(255)".to_string(),
                moren_zhi: None,
                buneng_weikong: true,
                zhujian: false,
                zizeng: false,
                zhushi: Some("密码".to_string()),
            },
            
            // jichuxinxi - 基础信息JSON字段
            ziduan_dingyi {
                ziduan_ming: "jichuxinxi".to_string(),
                shujuleixing: "TEXT".to_string(),
                moren_zhi: None,
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("基础信息JSON：邮箱(yx)、手机号(sjh)、名字(mingzi)、身份证(sfz)、IP(ip)".to_string()),
            },
            
            // fengjin - 封禁状态
            ziduan_dingyi {
                ziduan_ming: "fengjin".to_string(),
                shujuleixing: "BOOLEAN".to_string(),
                moren_zhi: Some("FALSE".to_string()),
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("封禁状态".to_string()),
            },
            
            // fengjinzt - 封禁状态详情JSON
            ziduan_dingyi {
                ziduan_ming: "fengjinzt".to_string(),
                shujuleixing: "TEXT".to_string(),
                moren_zhi: None,
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("封禁状态详情JSON：原因(yuanyin)、操作员(caozuoyuan)、封禁时间(fengjinshijian)".to_string()),
            },
            
            // jiefengshijian - 解封时间
            ziduan_dingyi {
                ziduan_ming: "jiefengshijian".to_string(),
                shujuleixing: "DATETIME".to_string(),
                moren_zhi: Some("NULL".to_string()),
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("解封时间".to_string()),
            },
            
            // shijianxinxi - 时间信息JSON
            ziduan_dingyi {
                ziduan_ming: "shijianxinxi".to_string(),
                shujuleixing: "TEXT".to_string(),
                moren_zhi: None,
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("时间信息JSON：注册时间(zcshijian)、登录时间(dlshijian)".to_string()),
            },
            
            // yonghuzu - 用户组
            ziduan_dingyi {
                ziduan_ming: "yonghuzu".to_string(),
                shujuleixing: "VARCHAR(20)".to_string(),
                moren_zhi: Some("'putong'".to_string()),
                buneng_weikong: true,
                zhujian: false,
                zizeng: false,
                zhushi: Some("用户组".to_string()),
            },
            
            // nicheng - 昵称
            ziduan_dingyi {
                ziduan_ming: "nicheng".to_string(),
                shujuleixing: "VARCHAR(50)".to_string(),
                moren_zhi: Some("''".to_string()),
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("昵称".to_string()),
            },
            
            // tx - 头像链接或base64或其他数据流
            ziduan_dingyi {
                ziduan_ming: "tx".to_string(),
                shujuleixing: "TEXT".to_string(),
                moren_zhi: None,
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("头像链接或base64或其他数据流".to_string()),
            },
            
            // kuozhanxinxi - 扩展信息
            ziduan_dingyi {
                ziduan_ming: "kuozhanxinxi".to_string(),
                shujuleixing: "TEXT".to_string(),
                moren_zhi: None,
                buneng_weikong: false,
                zhujian: false,
                zizeng: false,
                zhushi: Some("扩展信息JSON".to_string()),
            },
        ]
    }

    /// 获取用户表的索引定义
    fn huoqu_suoyin_dingyi() -> Vec<suoyin_dingyi> {
        vec![
            // zh字段唯一索引 - 用于账号登录查询
            suoyin_dingyi {
                suoyin_ming: "idx_yonghu_zh_unique".to_string(),
                ziduan_liebiao: vec!["zh".to_string()],
                weiyi: true,
                zhushi: Some("账号唯一索引".to_string()),
            },

            // yonghuzu + fengjin复合索引 - 用于权限和状态查询
            suoyin_dingyi {
                suoyin_ming: "idx_yonghu_yonghuzu_fengjin".to_string(),
                ziduan_liebiao: vec!["yonghuzu".to_string(), "fengjin".to_string()],
                weiyi: false,
                zhushi: Some("用户组和封禁状态复合索引".to_string()),
            },

            // nicheng字段索引 - 优化昵称更新和查询
            suoyin_dingyi {
                suoyin_ming: "idx_yonghu_nicheng".to_string(),
                ziduan_liebiao: vec!["nicheng".to_string()],
                weiyi: false,
                zhushi: Some("昵称索引，优化更新性能".to_string()),
            },

            // id + zh复合索引 - 优化基于主键和账号的更新
            suoyin_dingyi {
                suoyin_ming: "idx_yonghu_id_zh".to_string(),
                ziduan_liebiao: vec!["id".to_string(), "zh".to_string()],
                weiyi: false,
                zhushi: Some("主键账号复合索引，优化更新定位".to_string()),
            },
        ]
    }

    /// 初始化用户表
    /// 检测表是否存在，检查字段变化，执行热更新
    pub async fn chushihua_yonghu_biao(mysql_guanli: &mysql_shujuku_guanli) -> anyhow::Result<()> {
        shujukuxitong_rizhi("yonghu_biao", "开始初始化用户表...");
        
        let biao_dingyi = Self::huoqu_biao_dingyi();
        
        // 检测并同步表结构
        mysql_guanli.jiancha_tongbu_biao(&biao_dingyi).await?;
        
        shujukuxitong_rizhi("yonghu_biao", "用户表初始化完成");
        Ok(())
    }

    /// 验证必填字段
    pub fn yanzheng_bitian_ziduan(zh: &str, mm: &str, yonghuzu: &str) -> anyhow::Result<()> {
        if zh.trim().is_empty() {
            return Err(anyhow::anyhow!("账号不能为空"));
        }
        
        if mm.trim().is_empty() {
            return Err(anyhow::anyhow!("密码不能为空"));
        }
        
        if yonghuzu.trim().is_empty() {
            return Err(anyhow::anyhow!("用户组不能为空"));
        }
        
        Ok(())
    }

    /// 获取默认的基础信息JSON
    pub fn huoqu_moren_jichuxinxi() -> serde_json::Value {
        serde_json::json!({
            "yx": "",
            "sjh": "",
            "mingzi": "",
            "sfz": "",
            "ip": ""
        })
    }

    /// 获取默认的封禁状态JSON
    pub fn huoqu_moren_fengjinzt() -> serde_json::Value {
        serde_json::json!({
            "yuanyin": "",
            "caozuoyuan": "",
            "fengjinshijian": ""
        })
    }

    /// 获取默认的时间信息JSON
    pub fn huoqu_moren_shijianxinxi() -> serde_json::Value {
        serde_json::json!({
            "zcshijian": "",
            "dlshijian": ""
        })
    }

    /// 获取默认的扩展信息JSON
    pub fn huoqu_moren_kuozhanxinxi() -> serde_json::Value {
        serde_json::json!({})
    }
}
