#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::peizhi::peizhixitong_guanli::peizhixitong_guanli;
use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::{shujukuxitong_rizhi_xinxi, shujukuxitong_rizhi_cuowu};
use super::mysql_lianjie::mysql_lianjie_guanli;
use super::mysql_shujuku_guanli::mysql_shujuku_guanli;
use super::shujuku_neirong::yonghu_shujuku::yonghu_shujuku_biao::yonghu_shujuku_biao;

/// mysql数据库初始化器
pub struct mysql_chushihua;

impl mysql_chushihua {
    /// 初始化mysql数据库连接
    /// 
    /// 从配置系统读取mysql配置信息，创建连接池并测试连接
    /// 
    /// # 参数
    /// * `peizhixitong` - 配置系统管理器引用
    /// 
    /// # 返回值
    /// * `Ok(mysql_lianjie_guanli)` - 成功时返回mysql连接管理器
    /// * `Err(anyhow::Error)` - 失败时返回错误信息
    pub async fn chushihua(peizhixitong: &peizhixitong_guanli) -> anyhow::Result<mysql_lianjie_guanli> {
        shujukuxitong_rizhi_xinxi("mysql", "开始初始化mysql数据库连接...");

        // 1. 从配置系统获取总配置
        let zongpeizhi = match peizhixitong.peizhixitong_get_peizhi() {
            Ok(peizhi) => peizhi,
            Err(e) => {
                let cuowu_xinxi = format!("获取配置信息失败: {}", e);
                shujukuxitong_rizhi_cuowu("mysql", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        };

        // 2. 提取mysql数据库配置
        let mysql_peizhi = &zongpeizhi.shujuku;
        
        shujukuxitong_rizhi_xinxi(
            "mysql",
            &format!(
                "mysql配置信息 - 服务器: {}:{}, 用户: {}, 数据库: xianjingziliaozhan",
                mysql_peizhi.shujukuip,
                mysql_peizhi.duankou,
                mysql_peizhi.yonghu
            )
        );

        // 3. 验证配置信息
        if mysql_peizhi.yonghu != "root" {
            let cuowu_xinxi = format!(
                "数据库用户必须是root，当前用户: {}",
                mysql_peizhi.yonghu
            );
            shujukuxitong_rizhi_cuowu("mysql", &cuowu_xinxi);
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }

        // 4. 创建mysql连接管理器
        let mut mysql_lianjie = mysql_lianjie_guanli::new();

        // 5. 尝试连接到数据库
        match mysql_lianjie.lianjie(mysql_peizhi).await {
            Ok(_) => {
                shujukuxitong_rizhi_xinxi("mysql", "mysql数据库连接池创建成功");
            }
            Err(e) => {
                let cuowu_xinxi = format!("mysql数据库连接失败: {}", e);
                shujukuxitong_rizhi_cuowu("mysql", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        }

        // 6. 测试数据库连接
        match mysql_lianjie.ceshi_lianjie().await {
            Ok(_) => {
                shujukuxitong_rizhi_xinxi("mysql", "mysql数据库连接测试成功");
            }
            Err(e) => {
                let cuowu_xinxi = format!("mysql数据库连接测试失败: {}", e);
                shujukuxitong_rizhi_cuowu("mysql", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        }

        // 7. 显示连接池状态
        match mysql_lianjie.jiancha_zhuangtai() {
            Ok(zhuangtai) => {
                shujukuxitong_rizhi_xinxi("mysql", &zhuangtai);
            }
            Err(e) => {
                shujukuxitong_rizhi_cuowu("mysql", &format!("获取连接池状态失败: {}", e));
            }
        }

        // 8. 初始化数据库表结构
        let mysql_biao_guanli = mysql_shujuku_guanli::new(mysql_lianjie.clone());

        match Self::chushihua_shujuku_biao(&mysql_biao_guanli).await {
            Ok(_) => {
                shujukuxitong_rizhi_xinxi("mysql", "数据库表结构初始化成功");
            }
            Err(e) => {
                let cuowu_xinxi = format!("数据库表结构初始化失败: {}", e);
                shujukuxitong_rizhi_cuowu("mysql", &cuowu_xinxi);
                return Err(anyhow::anyhow!(cuowu_xinxi));
            }
        }

        shujukuxitong_rizhi_xinxi("mysql", "mysql数据库初始化完成！");

        Ok(mysql_lianjie)
    }

    /// 简化版初始化（用于测试）
    pub async fn chushihua_jiandan(
        shujukuip: &str,
        duankou: u16,
        yonghu: &str,
        mima: &str
    ) -> anyhow::Result<mysql_lianjie_guanli> {
        use crate::chushihua::peizhi::zongpeizhi_peizhi::peizhixitong_shujuku_peizhi;
        
        let peizhi = peizhixitong_shujuku_peizhi {
            shujukuip: shujukuip.to_string(),
            duankou,
            yonghu: yonghu.to_string(),
            mima: mima.to_string(),
        };

        let mut mysql_lianjie = mysql_lianjie_guanli::new();
        mysql_lianjie.lianjie(&peizhi).await?;
        mysql_lianjie.ceshi_lianjie().await?;
        
        Ok(mysql_lianjie)
    }

    /// 初始化数据库表结构
    ///
    /// 检测并创建/更新所有必要的数据库表
    ///
    /// # 参数
    /// * `mysql_biao_guanli` - mysql表管理器引用
    ///
    /// # 返回值
    /// * `Ok(())` - 成功时返回
    /// * `Err(anyhow::Error)` - 失败时返回错误信息
    async fn chushihua_shujuku_biao(mysql_biao_guanli: &mysql_shujuku_guanli) -> anyhow::Result<()> {
        shujukuxitong_rizhi_xinxi("mysql_biao", "开始初始化数据库表结构...");

        // 初始化用户表
        yonghu_shujuku_biao::chushihua_yonghu_biao(mysql_biao_guanli).await?;

        // 这里可以添加其他表的初始化
        // 例如：
        // qita_biao::chushihua_qita_biao(mysql_biao_guanli).await?;

        shujukuxitong_rizhi_xinxi("mysql_biao", "数据库表结构初始化完成");
        Ok(())
    }
}
