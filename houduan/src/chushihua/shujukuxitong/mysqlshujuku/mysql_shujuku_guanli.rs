#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::shujukuxitong_rizhi;
use super::mysql_lianjie::mysql_lianjie_guanli;
use sqlx::{Row, MySql, FromRow};
use serde_json::Value;
use std::collections::HashMap;

/// mysql数据库表管理器
pub struct mysql_shujuku_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
}

/// 字段定义结构
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ziduan_dingyi {
    pub ziduan_ming: String,
    pub shujuleixing: String,
    pub moren_zhi: Option<String>,
    pub buneng_weikong: bool,
    pub zhujian: bool,
    pub zizeng: bool,
    pub zhushi: Option<String>,
}

/// 索引定义结构
#[derive(Debug, <PERSON>lone)]
pub struct suoyin_dingyi {
    pub suoyin_ming: String,
    pub ziduan_liebiao: Vec<String>,
    pub weiyi: bool,
    pub zhushi: Option<String>,
}

/// 表定义结构
#[derive(Debug, Clone)]
pub struct biao_dingyi {
    pub biao_ming: String,
    pub ziduan_liebiao: Vec<ziduan_dingyi>,
    pub suoyin_liebiao: Vec<suoyin_dingyi>,
    pub zhushi: Option<String>,
}

impl mysql_shujuku_guanli {
    /// 创建新的mysql数据库表管理器
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self { mysql_lianjie }
    }

    /// 检测表是否存在
    pub async fn jiancha_biao_cunzai(&self, biao_ming: &str) -> anyhow::Result<bool> {
        let sql = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'xianjingziliaozhan' AND table_name = ?";
        
        let jieguo = sqlx::query(sql)
            .bind(biao_ming)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;
            
        let count: i64 = jieguo.get("count");
        Ok(count > 0)
    }

    /// 获取表的当前结构
    pub async fn huoqu_biao_jiegou(&self, biao_ming: &str) -> anyhow::Result<Vec<ziduan_dingyi>> {
        let sql = r#"
            SELECT 
                COLUMN_NAME as ziduan_ming,
                COLUMN_TYPE as shujuleixing,
                COLUMN_DEFAULT as moren_zhi,
                IS_NULLABLE as keyi_weikong,
                COLUMN_KEY as jian_leixing,
                EXTRA as ezwai_xinxi,
                COLUMN_COMMENT as zhushi
            FROM information_schema.columns 
            WHERE table_schema = 'xianjingziliaozhan' AND table_name = ?
            ORDER BY ORDINAL_POSITION
        "#;

        let rows = sqlx::query(sql)
            .bind(biao_ming)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let mut ziduan_liebiao = Vec::new();
        
        for row in rows {
            let ziduan_ming: String = row.try_get("ziduan_ming").unwrap_or_else(|_| "unknown".to_string());
            let shujuleixing: String = row.try_get("shujuleixing").unwrap_or_else(|_| "TEXT".to_string());
            let moren_zhi: Option<String> = row.try_get("moren_zhi").ok();
            let keyi_weikong: String = row.try_get("keyi_weikong").unwrap_or_else(|_| "YES".to_string());
            let jian_leixing: String = row.try_get("jian_leixing").unwrap_or_else(|_| "".to_string());
            let ezwai_xinxi: String = row.try_get("ezwai_xinxi").unwrap_or_else(|_| "".to_string());
            let zhushi: Option<String> = row.try_get("zhushi").ok();

            let ziduan = ziduan_dingyi {
                ziduan_ming,
                shujuleixing,
                moren_zhi,
                buneng_weikong: keyi_weikong == "NO",
                zhujian: jian_leixing == "PRI",
                zizeng: ezwai_xinxi.contains("auto_increment"),
                zhushi,
            };
            
            ziduan_liebiao.push(ziduan);
        }

        Ok(ziduan_liebiao)
    }

    /// 创建新表
    pub async fn chuangjian_biao(&self, biao_dingyi: &biao_dingyi) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_biao_guanli", &format!("开始创建表: {}", biao_dingyi.biao_ming));

        let mut sql = format!("CREATE TABLE `{}` (", biao_dingyi.biao_ming);
        let mut ziduan_sql_liebiao = Vec::new();
        let mut zhujian_liebiao = Vec::new();

        for ziduan in &biao_dingyi.ziduan_liebiao {
            let mut ziduan_sql = format!("`{}` {}", ziduan.ziduan_ming, ziduan.shujuleixing);
            
            if ziduan.buneng_weikong {
                ziduan_sql.push_str(" NOT NULL");
            }
            
            if ziduan.zizeng {
                ziduan_sql.push_str(" AUTO_INCREMENT");
            }
            
            if let Some(moren_zhi) = &ziduan.moren_zhi {
                if !ziduan.zizeng {
                    ziduan_sql.push_str(&format!(" DEFAULT {}", moren_zhi));
                }
            }
            
            if let Some(zhushi) = &ziduan.zhushi {
                ziduan_sql.push_str(&format!(" COMMENT '{}'", zhushi));
            }
            
            ziduan_sql_liebiao.push(ziduan_sql);
            
            if ziduan.zhujian {
                zhujian_liebiao.push(format!("`{}`", ziduan.ziduan_ming));
            }
        }

        sql.push_str(&ziduan_sql_liebiao.join(", "));
        
        if !zhujian_liebiao.is_empty() {
            sql.push_str(&format!(", PRIMARY KEY ({})", zhujian_liebiao.join(", ")));
        }
        
        sql.push_str(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        if let Some(zhushi) = &biao_dingyi.zhushi {
            sql.push_str(&format!(" COMMENT='{}'", zhushi));
        }

        sqlx::query(&sql)
            .execute(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        shujukuxitong_rizhi("mysql_biao_guanli", &format!("表 {} 创建成功", biao_dingyi.biao_ming));
        Ok(())
    }

    /// 比较表结构差异
    pub async fn bijiao_biao_jiegou(&self, biao_ming: &str, mubiao_jiegou: &[ziduan_dingyi]) -> anyhow::Result<(Vec<ziduan_dingyi>, Vec<String>)> {
        let dangqian_jiegou = self.huoqu_biao_jiegou(biao_ming).await?;
        
        let mut dangqian_ziduan_map = HashMap::new();
        for ziduan in &dangqian_jiegou {
            dangqian_ziduan_map.insert(ziduan.ziduan_ming.clone(), ziduan);
        }
        
        let mut mubiao_ziduan_map = HashMap::new();
        for ziduan in mubiao_jiegou {
            mubiao_ziduan_map.insert(ziduan.ziduan_ming.clone(), ziduan);
        }
        
        // 找出需要添加的字段
        let mut xuyao_tianjia = Vec::new();
        for ziduan in mubiao_jiegou {
            if !dangqian_ziduan_map.contains_key(&ziduan.ziduan_ming) {
                xuyao_tianjia.push(ziduan.clone());
            }
        }
        
        // 找出需要删除的字段
        let mut xuyao_shanchu = Vec::new();
        for ziduan in &dangqian_jiegou {
            if !mubiao_ziduan_map.contains_key(&ziduan.ziduan_ming) {
                xuyao_shanchu.push(ziduan.ziduan_ming.clone());
            }
        }
        
        Ok((xuyao_tianjia, xuyao_shanchu))
    }

    /// 热添加字段
    pub async fn re_tianjia_ziduan(&self, biao_ming: &str, ziduan: &ziduan_dingyi) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_biao_guanli", &format!("开始为表 {} 添加字段: {}", biao_ming, ziduan.ziduan_ming));

        let mut sql = format!("ALTER TABLE `{}` ADD COLUMN `{}` {}", biao_ming, ziduan.ziduan_ming, ziduan.shujuleixing);
        
        if ziduan.buneng_weikong {
            sql.push_str(" NOT NULL");
        }
        
        if ziduan.zizeng {
            sql.push_str(" AUTO_INCREMENT");
        }
        
        if let Some(moren_zhi) = &ziduan.moren_zhi {
            if !ziduan.zizeng {
                sql.push_str(&format!(" DEFAULT {}", moren_zhi));
            }
        }
        
        if let Some(zhushi) = &ziduan.zhushi {
            sql.push_str(&format!(" COMMENT '{}'", zhushi));
        }

        sqlx::query(&sql)
            .execute(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        shujukuxitong_rizhi("mysql_biao_guanli", &format!("字段 {} 添加成功", ziduan.ziduan_ming));
        Ok(())
    }

    /// 热删除字段
    pub async fn re_shanchu_ziduan(&self, biao_ming: &str, ziduan_ming: &str) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_biao_guanli", &format!("警告：即将删除表 {} 的字段 {}，数据将丢失！", biao_ming, ziduan_ming));

        let sql = format!("ALTER TABLE `{}` DROP COLUMN `{}`", biao_ming, ziduan_ming);
        
        sqlx::query(&sql)
            .execute(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        shujukuxitong_rizhi("mysql_biao_guanli", &format!("字段 {} 删除成功，相关数据已丢失", ziduan_ming));
        Ok(())
    }

    /// 检测并同步表结构
    pub async fn jiancha_tongbu_biao(&self, biao_dingyi: &biao_dingyi) -> anyhow::Result<()> {
        let biao_ming = &biao_dingyi.biao_ming;
        
        // 检查表是否存在
        if !self.jiancha_biao_cunzai(biao_ming).await? {
            shujukuxitong_rizhi("mysql_biao_guanli", &format!("表 {} 不存在，开始创建", biao_ming));
            self.chuangjian_biao(biao_dingyi).await?;
            return Ok(());
        }

        // 比较表结构
        let (xuyao_tianjia, xuyao_shanchu) = self.bijiao_biao_jiegou(biao_ming, &biao_dingyi.ziduan_liebiao).await?;
        
        // 记录是否有变更
        let you_biangeng = !xuyao_tianjia.is_empty() || !xuyao_shanchu.is_empty();

        // 删除不需要的字段
        for ziduan_ming in &xuyao_shanchu {
            self.re_shanchu_ziduan(biao_ming, ziduan_ming).await?;
        }

        // 添加新字段
        for ziduan in &xuyao_tianjia {
            self.re_tianjia_ziduan(biao_ming, ziduan).await?;
        }

        // 同步索引
        self.tongbu_biao_suoyin(biao_ming, &biao_dingyi.suoyin_liebiao).await?;

        if !you_biangeng {
            shujukuxitong_rizhi("mysql_biao_guanli", &format!("表 {} 结构已是最新，无需更新", biao_ming));
        } else {
            shujukuxitong_rizhi("mysql_biao_guanli", &format!("表 {} 结构同步完成", biao_ming));
        }

        Ok(())
    }

    /// 检查索引是否存在
    pub async fn jiancha_suoyin_cunzai(&self, biao_ming: &str, suoyin_ming: &str) -> anyhow::Result<bool> {
        let sql = "SELECT COUNT(*) as count FROM information_schema.statistics WHERE table_schema = 'xianjingziliaozhan' AND table_name = ? AND index_name = ?";

        let jieguo = sqlx::query(sql)
            .bind(biao_ming)
            .bind(suoyin_ming)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let count: i64 = jieguo.get("count");
        Ok(count > 0)
    }

    /// 创建索引
    pub async fn chuangjian_suoyin(&self, biao_ming: &str, suoyin: &suoyin_dingyi) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("开始为表 {} 创建索引: {}", biao_ming, suoyin.suoyin_ming));

        let suoyin_leixing = if suoyin.weiyi { "UNIQUE INDEX" } else { "INDEX" };
        let ziduan_liebiao = suoyin.ziduan_liebiao.iter()
            .map(|ziduan| format!("`{}`", ziduan))
            .collect::<Vec<_>>()
            .join(", ");

        let sql = format!(
            "CREATE {} `{}` ON `{}` ({})",
            suoyin_leixing,
            suoyin.suoyin_ming,
            biao_ming,
            ziduan_liebiao
        );

        sqlx::query(&sql)
            .execute(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("索引 {} 创建成功", suoyin.suoyin_ming));
        Ok(())
    }

    /// 删除索引
    pub async fn shanchu_suoyin(&self, biao_ming: &str, suoyin_ming: &str) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("开始删除表 {} 的索引: {}", biao_ming, suoyin_ming));

        let sql = format!("DROP INDEX `{}` ON `{}`", suoyin_ming, biao_ming);

        sqlx::query(&sql)
            .execute(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("索引 {} 删除成功", suoyin_ming));
        Ok(())
    }

    /// 同步表的索引
    pub async fn tongbu_biao_suoyin(&self, biao_ming: &str, mubiao_suoyin: &[suoyin_dingyi]) -> anyhow::Result<()> {
        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("开始同步表 {} 的索引...", biao_ming));

        for suoyin in mubiao_suoyin {
            if !self.jiancha_suoyin_cunzai(biao_ming, &suoyin.suoyin_ming).await? {
                self.chuangjian_suoyin(biao_ming, suoyin).await?;
            } else {
                shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("索引 {} 已存在，跳过创建", suoyin.suoyin_ming));
            }
        }

        shujukuxitong_rizhi("mysql_suoyin_guanli", &format!("表 {} 索引同步完成", biao_ming));
        Ok(())
    }
}
