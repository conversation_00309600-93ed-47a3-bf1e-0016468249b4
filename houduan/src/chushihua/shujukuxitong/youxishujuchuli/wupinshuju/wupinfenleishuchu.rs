#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use serde::{Deserialize, Serialize};
use sqlx::Row;
use std::collections::HashMap;

/// 物品分类信息结构体
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct wupin_fenlei_xinxi {
    /// 类型
    pub leixing: String,
    /// 子类型列表
    pub zileixing_liebiao: Vec<String>,
}

/// 分类查询结果结构体
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct fenlei_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 所有类型列表
    pub leixing_liebiao: Option<Vec<String>>,
    /// 指定类型的子类型列表
    pub zileixing_liebiao: Option<Vec<String>>,
    /// 完整分类信息（类型及其对应的子类型）
    pub wanzheng_fenlei: Option<Vec<wupin_fenlei_xinxi>>,
}

/// 物品分类输出管理器
pub struct wupin_fenlei_shuchu_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl wupin_fenlei_shuchu_guanli {
    /// 创建新的物品分类输出管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的物品分类输出管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 获取所有物品类型列表
    pub async fn huoqu_suoyou_leixing(&self) -> anyhow::Result<fenlei_chaxun_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_leixing;
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(leixing_liebiao) = serde_json::from_str::<Vec<String>>(&huancun_shuju) {
                    return Ok(fenlei_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        leixing_liebiao: Some(leixing_liebiao),
                        zileixing_liebiao: None,
                        wanzheng_fenlei: None,
                    });
                }
            }
        }

        // 从数据库查询
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_suoyou_leixing;

        match sqlx::query(sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut leixing_liebiao = Vec::new();
                for row in rows {
                    if let Ok(leixing) = row.try_get::<String, _>("leixing") {
                        leixing_liebiao.push(leixing);
                    }
                }

                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_leixing;
                    if let Ok(json_shuju) = serde_json::to_string(&leixing_liebiao) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, wupin_zifuchuan_changliangguanli::fenlei_huancun_shijian as i64).await;
                    }
                }

                Ok(fenlei_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    leixing_liebiao: Some(leixing_liebiao),
                    zileixing_liebiao: None,
                    wanzheng_fenlei: None,
                })
            }
            Err(e) => {
                Ok(fenlei_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_leixing_liebiao_shibai(&e.to_string())),
                    leixing_liebiao: None,
                    zileixing_liebiao: None,
                    wanzheng_fenlei: None,
                })
            }
        }
    }

    /// 根据类型获取子类型列表
    pub async fn huoqu_zileixing_liebiao(&self, leixing: &str) -> anyhow::Result<fenlei_chaxun_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_zileixing(leixing);
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(zileixing_liebiao) = serde_json::from_str::<Vec<String>>(&huancun_shuju) {
                    return Ok(fenlei_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        leixing_liebiao: None,
                        zileixing_liebiao: Some(zileixing_liebiao),
                        wanzheng_fenlei: None,
                    });
                }
            }
        }

        // 从数据库查询
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_zileixing_by_leixing;

        match sqlx::query(sql)
            .bind(leixing)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut zileixing_liebiao = Vec::new();
                for row in rows {
                    if let Ok(zileixing) = row.try_get::<String, _>("zileixing") {
                        zileixing_liebiao.push(zileixing);
                    }
                }

                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_fenlei_zileixing(leixing);
                    if let Ok(json_shuju) = serde_json::to_string(&zileixing_liebiao) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, wupin_zifuchuan_changliangguanli::fenlei_huancun_shijian as i64).await;
                    }
                }

                Ok(fenlei_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    leixing_liebiao: None,
                    zileixing_liebiao: Some(zileixing_liebiao),
                    wanzheng_fenlei: None,
                })
            }
            Err(e) => {
                Ok(fenlei_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zileixing_liebiao_shibai(&e.to_string())),
                    leixing_liebiao: None,
                    zileixing_liebiao: None,
                    wanzheng_fenlei: None,
                })
            }
        }
    }

    /// 获取完整分类信息（所有类型及其对应的子类型）
    pub async fn huoqu_wanzheng_fenlei(&self) -> anyhow::Result<fenlei_chaxun_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_wanzheng;
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(wanzheng_fenlei) = serde_json::from_str::<Vec<wupin_fenlei_xinxi>>(&huancun_shuju) {
                    return Ok(fenlei_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        leixing_liebiao: None,
                        zileixing_liebiao: None,
                        wanzheng_fenlei: Some(wanzheng_fenlei),
                    });
                }
            }
        }

        // 从数据库查询
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_wanzheng_fenlei;

        match sqlx::query(sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut fenlei_map: HashMap<String, Vec<String>> = HashMap::new();

                for row in rows {
                    if let (Ok(leixing), Ok(zileixing)) = (
                        row.try_get::<String, _>("leixing"),
                        row.try_get::<String, _>("zileixing")
                    ) {
                        fenlei_map.entry(leixing)
                            .or_insert_with(Vec::new)
                            .push(zileixing);
                    }
                }

                // 去重并排序
                let mut wanzheng_fenlei = Vec::new();
                for (leixing, mut zileixing_liebiao) in fenlei_map {
                    zileixing_liebiao.sort();
                    zileixing_liebiao.dedup();
                    wanzheng_fenlei.push(wupin_fenlei_xinxi {
                        leixing,
                        zileixing_liebiao,
                    });
                }
                wanzheng_fenlei.sort_by(|a, b| a.leixing.cmp(&b.leixing));

                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_wanzheng;
                    if let Ok(json_shuju) = serde_json::to_string(&wanzheng_fenlei) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, wupin_zifuchuan_changliangguanli::fenlei_huancun_shijian as i64).await;
                    }
                }

                Ok(fenlei_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    leixing_liebiao: None,
                    zileixing_liebiao: None,
                    wanzheng_fenlei: Some(wanzheng_fenlei),
                })
            }
            Err(e) => {
                Ok(fenlei_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_wanzheng_fenlei_shibai(&e.to_string())),
                    leixing_liebiao: None,
                    zileixing_liebiao: None,
                    wanzheng_fenlei: None,
                })
            }
        }
    }

    /// 获取分类统计信息
    pub async fn huoqu_fenlei_tongji(&self) -> anyhow::Result<HashMap<String, u64>> {
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_fenlei_tongji;

        match sqlx::query(sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut tongji_map = HashMap::new();
                for row in rows {
                    if let (Ok(leixing), Ok(shuliang)) = (
                        row.try_get::<String, _>("leixing"),
                        row.try_get::<i64, _>("shuliang")
                    ) {
                        tongji_map.insert(leixing, shuliang as u64);
                    }
                }
                Ok(tongji_map)
            }
            Err(e) => {
                Err(anyhow::anyhow!("{}", wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_fenlei_tongji_shibai(&e.to_string())))
            }
        }
    }
}