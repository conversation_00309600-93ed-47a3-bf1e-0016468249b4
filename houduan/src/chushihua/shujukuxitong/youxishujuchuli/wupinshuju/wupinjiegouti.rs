#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 物品列表项信息结构体（通用）
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct wupin_liebiao_xiang {
    /// 物品ID
    pub wupin_id: i32,
    /// 物品名称（优先item_name表，没有则用汇总表）
    pub wupin_mingcheng: String,
    /// 物品类型
    pub leixing: Option<String>,
    /// 物品子类型
    pub zileixing: Option<String>,
    /// 物品类名
    pub leiming: Option<String>,
}

/// 分页参数结构体（通用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenye_canshu {
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

impl fenye_canshu {
    /// 创建新的分页参数
    pub fn new(yema: u32, meiye_shuliang: u32) -> Self {
        Self {
            yema: if yema == 0 { 1 } else { yema },
            meiye_shuliang: if meiye_shuliang == 0 { 10 } else { meiye_shuliang },
        }
    }

    /// 计算偏移量
    pub fn jisuan_pianyi(&self) -> u32 {
        (self.yema - 1) * self.meiye_shuliang
    }

    /// 计算总页数
    pub fn jisuan_zong_yeshu(&self, zong_shuliang: u64) -> u32 {
        if zong_shuliang == 0 {
            return 1;
        }
        ((zong_shuliang as f64) / (self.meiye_shuliang as f64)).ceil() as u32
    }
}

/// 物品列表查询结果结构体（通用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 物品列表
    pub wupin_liebiao: Vec<wupin_liebiao_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 数据来源（用于测试显示）
    pub shuju_laiyuan: Option<String>,
}

impl wupin_liebiao_jieguo {
    /// 创建成功的查询结果
    pub fn chenggong(
        wupin_liebiao: Vec<wupin_liebiao_xiang>,
        fenye_canshu: &fenye_canshu,
        zong_shuliang: u64,
    ) -> Self {
        let zong_yeshu = fenye_canshu.jisuan_zong_yeshu(zong_shuliang);
        Self {
            chenggong: true,
            cuowu_xinxi: None,
            wupin_liebiao,
            dangqian_yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            shuju_laiyuan: Some("mysql".to_string()),
        }
    }

    /// 创建成功的查询结果（带数据来源）
    pub fn chenggong_with_laiyuan(
        wupin_liebiao: Vec<wupin_liebiao_xiang>,
        fenye_canshu: &fenye_canshu,
        zong_shuliang: u64,
        shuju_laiyuan: String,
    ) -> Self {
        let zong_yeshu = fenye_canshu.jisuan_zong_yeshu(zong_shuliang);
        Self {
            chenggong: true,
            cuowu_xinxi: None,
            wupin_liebiao,
            dangqian_yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            shuju_laiyuan: Some(shuju_laiyuan),
        }
    }

    /// 创建失败的查询结果
    pub fn shibai(cuowu_xinxi: String) -> Self {
        Self {
            chenggong: false,
            cuowu_xinxi: Some(cuowu_xinxi),
            wupin_liebiao: Vec::new(),
            dangqian_yema: 1,
            meiye_shuliang: 10,
            zong_shuliang: 0,
            zong_yeshu: 1,
            shuju_laiyuan: None,
        }
    }
}

/// item_name表基础信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_jiben_xinxi {
    /// 物品ID
    pub id: String,
    /// 中文名称
    pub zhognwenming: Option<String>,
    /// 中文介绍
    pub zhognwenjieshao: Option<String>,
}

/// wupin_huizong表汇总信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_huizong_xinxi {
    /// 物品ID
    pub wupin_id: Option<String>,
    /// 物品名称
    pub wupin_mingcheng: Option<String>,
    /// 中文名
    pub zhongwenming: Option<String>,
    /// 完整性分数
    pub wanzhengxing_fenshu: Option<String>,
    /// 类型
    pub leixing: Option<String>,
    /// 子类型
    pub zileixing: Option<String>,
    /// 重量
    pub zhongliang: Option<String>,
    /// 怪物数量
    pub guaiwu_shuliang: Option<String>,
    /// 商家数量
    pub shangjia_shuliang: Option<String>,
    /// 宠物数量
    pub chongwu_shuliang: Option<String>,
    /// 箱子数量
    pub xiangzi_shuliang: Option<String>,
    /// 历史记录数量
    pub lishi_jilu_shuliang: Option<String>,
    /// 基础信息yaml
    pub jichuxinxi_yaml: Option<String>,
    /// 怪物来源yaml
    pub guaiwulaiyuan_yaml: Option<String>,
    /// 售卖商家yaml
    pub shoumaishangjia_yaml: Option<String>,
    /// 宠物yaml
    pub chongwu_yaml: Option<String>,
    /// 来源箱子yaml
    pub laiyuanxiangzi_yaml: Option<String>,
    /// 历史变动yaml
    pub lishibiandong_yaml: Option<String>,
    /// 有基础信息
    pub you_jichuxinxi: Option<String>,
    /// 有怪物来源
    pub you_guaiwulaiyuan: Option<String>,
    /// 有售卖商家
    pub you_shoumaishangjia: Option<String>,
    /// 有宠物
    pub you_chongwu: Option<String>,
    /// 有来源箱子
    pub you_laiyuanxiangzi: Option<String>,
    /// 有历史变动
    pub you_lishibiandong: Option<String>,
}

/// 完整物品信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_wanzheng_xinxi {
    /// 物品ID
    pub id: String,
    /// 基础信息
    pub jiben_xinxi: Option<wupin_jiben_xinxi>,
    /// 汇总信息
    pub huizong_xinxi: Option<wupin_huizong_xinxi>,
}

/// 物品查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 完整物品信息（当查询全部信息时）
    pub wanzheng_xinxi: Option<wupin_wanzheng_xinxi>,
    /// 物品数据映射（当查询指定字段时）
    pub wupin_shuju: Option<HashMap<String, String>>,
}

/// 支持的字段映射
pub struct wupin_ziduan_yingshe;

impl wupin_ziduan_yingshe {
    /// 获取item_name表支持的字段
    pub fn huoqu_jiben_biao_ziduan() -> Vec<String> {
        vec![
            "ID".to_string(),
            "zhognwenming".to_string(),
            "zhognwenjieshao".to_string(),
        ]
    }

    /// 获取wupin_huizong表支持的字段
    pub fn huoqu_huizong_biao_ziduan() -> Vec<String> {
        vec![
            "wupin_id".to_string(),
            "wupin_mingcheng".to_string(),
            "zhongwenming".to_string(),
            "wanzhengxing_fenshu".to_string(),
            "leixing".to_string(),
            "zileixing".to_string(),
            "zhongliang".to_string(),
            "guaiwu_shuliang".to_string(),
            "shangjia_shuliang".to_string(),
            "chongwu_shuliang".to_string(),
            "xiangzi_shuliang".to_string(),
            "lishi_jilu_shuliang".to_string(),
            "jichuxinxi_yaml".to_string(),
            "guaiwulaiyuan_yaml".to_string(),
            "shoumaishangjia_yaml".to_string(),
            "chongwu_yaml".to_string(),
            "laiyuanxiangzi_yaml".to_string(),
            "lishibiandong_yaml".to_string(),
            "you_jichuxinxi".to_string(),
            "you_guaiwulaiyuan".to_string(),
            "you_shoumaishangjia".to_string(),
            "you_chongwu".to_string(),
            "you_laiyuanxiangzi".to_string(),
            "you_lishibiandong".to_string(),
        ]
    }

    /// 获取所有支持的字段映射
    pub fn huoqu_suoyou_ziduan_yingshe() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert("item_name".to_string(), Self::huoqu_jiben_biao_ziduan());
        ziduan_map.insert("wupin_huizong".to_string(), Self::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 检查字段是否有效
    pub fn jiancha_ziduan_youxiao(ziduan_ming: &str) -> bool {
        let suoyou_ziduan = Self::huoqu_suoyou_ziduan_yingshe();
        for (_biao_ming, ziduan_liebiao) in suoyou_ziduan {
            if ziduan_liebiao.contains(&ziduan_ming.to_string()) {
                return true;
            }
        }
        false
    }
}