#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_liebiao_shuju::jineng_liebiao_guanli;
use super::jineng_redis_kongzhi::jineng_redis_kongzhi;
use super::jinengshujuchuli::jineng_shuju_guanli;
use super::jinengshujujiegouti::jineng_liebiao_fenye_canshu;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;

/// 技能数据处理测试类
pub struct jineng_ceshi;

impl jineng_ceshi {
    /// 测试技能数据获取功能
    pub async fn ceshi_jineng_shuju_huoqu(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: Option<redis_lianjie_guanli>,
    ) -> anyhow::Result<()> {
        println!("开始测试技能数据获取功能...");

        // 创建技能数据管理器
        let jineng_guanli = match redis_guanli {
            Some(redis) => {
                let redis_kongzhi = jineng_redis_kongzhi::new(redis);
                jineng_shuju_guanli::new_with_redis(mysql_guanli, redis_kongzhi)
            }
            None => jineng_shuju_guanli::new(mysql_guanli),
        };

        // 测试技能ID（你可以根据实际数据库中的技能ID进行调整）
        let test_jineng_id = "1";

        println!("测试1: 获取技能全部信息");
        match jineng_guanli.tongyong_chaxun(test_jineng_id, "quanbu_xinxi").await {
            Ok(jieguo) => {
                println!("原始返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 查询技能{}时发生错误: {}", test_jineng_id, e);
            }
        }

        println!("\n测试2: 获取技能指定字段");
        match jineng_guanli.tongyong_chaxun(test_jineng_id, "jineng_mingcheng,zhongwenming").await {
            Ok(jieguo) => {
                println!("原始返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 查询技能{}指定字段时发生错误: {}", test_jineng_id, e);
            }
        }

        println!("\n测试3: 检查技能是否存在");
        match jineng_guanli.jiancha_jineng_cunzai(test_jineng_id).await {
            Ok(cunzai) => {
                println!("原始返回结果: {}", cunzai);
            }
            Err(e) => {
                println!("✗ 检查技能{}是否存在时发生错误: {}", test_jineng_id, e);
            }
        }

        println!("\n测试4: 获取支持的字段列表");
        let zhichi_ziduan = jineng_shuju_guanli::huoqu_zhichi_ziduan();
        println!("原始返回结果:");
        println!("{:#?}", zhichi_ziduan);

        println!("\n测试5: 获取缓存统计信息");
        match jineng_guanli.huoqu_jineng_huancun_tongji().await {
            Ok(tongji) => {
                println!("原始返回结果:");
                println!("{:#?}", tongji);
            }
            Err(e) => {
                println!("✗ 获取缓存统计信息失败: {}", e);
            }
        }

        println!("\n技能数据获取功能测试完成！");
        Ok(())
    }

    /// 测试技能缓存功能
    pub async fn ceshi_jineng_huancun_gongneng(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: redis_lianjie_guanli,
    ) -> anyhow::Result<()> {
        println!("开始测试技能缓存功能...");

        let redis_kongzhi = jineng_redis_kongzhi::new(redis_guanli);
        let jineng_guanli = jineng_shuju_guanli::new_with_redis(mysql_guanli, redis_kongzhi);

        let test_jineng_id = "1";

        println!("测试1: 第一次获取（从数据库）");
        let start_time = std::time::Instant::now();
        match jineng_guanli.tongyong_chaxun(test_jineng_id, "quanbu_xinxi").await {
            Ok(jieguo) => {
                let duration = start_time.elapsed();
                println!("✓ 第一次查询耗时: {:?}", duration);
                if jieguo.chenggong {
                    println!("  成功获取技能数据");
                }
            }
            Err(e) => {
                println!("✗ 第一次查询失败: {}", e);
            }
        }

        println!("\n测试2: 第二次获取（从缓存）");
        let start_time = std::time::Instant::now();
        match jineng_guanli.tongyong_chaxun(test_jineng_id, "quanbu_xinxi").await {
            Ok(jieguo) => {
                let duration = start_time.elapsed();
                println!("✓ 第二次查询耗时: {:?}", duration);
                if jieguo.chenggong {
                    println!("  成功从缓存获取技能数据");
                }
            }
            Err(e) => {
                println!("✗ 第二次查询失败: {}", e);
            }
        }

        println!("\n测试3: 删除指定技能缓存");
        match jineng_guanli.shanchu_jineng_huancun(test_jineng_id).await {
            Ok(shanchu_chenggong) => {
                if shanchu_chenggong {
                    println!("✓ 成功删除技能{}的缓存", test_jineng_id);
                } else {
                    println!("✓ 技能{}无缓存，无需删除", test_jineng_id);
                }
            }
            Err(e) => {
                println!("✗ 删除技能{}缓存失败: {}", test_jineng_id, e);
            }
        }

        println!("\n技能缓存功能测试完成！");
        Ok(())
    }

    /// 测试技能列表功能
    pub async fn ceshi_jineng_liebiao_gongneng(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: Option<redis_lianjie_guanli>,
    ) -> anyhow::Result<()> {
        println!("开始测试技能列表功能...");

        // 创建技能列表管理器
        let liebiao_guanli = match redis_guanli {
            Some(redis) => {
                let redis_kongzhi = jineng_redis_kongzhi::new(redis);
                jineng_liebiao_guanli::new_with_redis(mysql_guanli, redis_kongzhi)
            }
            None => jineng_liebiao_guanli::new(mysql_guanli),
        };

        println!("测试1: 获取第一页技能列表（每页10个）");
        let fenye_canshu = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 10,
            dangqian_ye: 1,
        };

        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu).await {
            Ok(jieguo) => {
                println!("原始返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 获取技能列表失败: {}", e);
            }
        }

        println!("\n测试2: 获取第二页技能列表（每页5个）");
        let fenye_canshu2 = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 5,
            dangqian_ye: 2,
        };

        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu2).await {
            Ok(jieguo) => {
                println!("原始返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 获取技能列表失败: {}", e);
            }
        }

        println!("\n测试3: 获取技能列表缓存统计信息");
        match liebiao_guanli.huoqu_liebiao_huancun_tongji().await {
            Ok(tongji) => {
                println!("原始返回结果:");
                println!("{:#?}", tongji);
            }
            Err(e) => {
                println!("✗ 获取缓存统计信息失败: {}", e);
            }
        }

        println!("\n测试4: 清理技能列表缓存");
        match liebiao_guanli.qingchu_jineng_liebiao_huancun().await {
            Ok(shanchu_shuliang) => {
                println!("原始返回结果: 清理了{}个缓存", shanchu_shuliang);
            }
            Err(e) => {
                println!("✗ 清理缓存失败: {}", e);
            }
        }

        println!("\n技能列表功能测试完成！");
        Ok(())
    }
}
