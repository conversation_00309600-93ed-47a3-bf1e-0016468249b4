#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_redis_kongzhi::jineng_redis_kongzhi;
use super::jineng_rizhi_kongzhi::jineng_zifuchuan_changliangguanli;
use super::jineng_sql_kongzhi::jineng_sql_guanli;
use super::jinengshujujiegouti::{
    jineng_chaxun_jieguo, jineng_huizong_xinxi, jineng_jiben_xinxi,
    jineng_wanzheng_xinxi, jineng_ziduan_yingshe,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use sqlx::Row;
use std::collections::HashMap;

/// 技能数据管理器
pub struct jineng_shuju_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_kongzhi: Option<jineng_redis_kongzhi>,
}

impl jineng_shuju_guanli {
    /// 创建新的技能数据管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的技能数据管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: jineng_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 通用查询方法
    ///
    /// # 参数
    /// * `jineng_id` - 技能ID
    /// * `chaxun_moshi` - 查询模式：
    ///   - "quanbu_xinxi": 查询全部信息（同时查询skill_name表和jineng_huizong表）
    ///   - 字段名列表（用逗号分隔）: 只查询jineng_huizong表的指定字段
    pub async fn tongyong_chaxun(&self, jineng_id: &str, chaxun_moshi: &str) -> anyhow::Result<jineng_chaxun_jieguo> {
        if chaxun_moshi == jineng_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi {
            self.chaxun_quanbu_xinxi(jineng_id).await
        } else {
            self.chaxun_zhiding_ziduan(jineng_id, chaxun_moshi).await
        }
    }

    /// 查询技能全部信息（带Redis缓存）
    async fn chaxun_quanbu_xinxi(&self, jineng_id: &str) -> anyhow::Result<jineng_chaxun_jieguo> {
        // 如果有Redis控制器，先尝试从Redis获取
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(wanzheng_xinxi)) = redis_kongzhi.huoqu_quanbu_xinxi(jineng_id).await {
                return Ok(jineng_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    wanzheng_xinxi: Some(wanzheng_xinxi),
                    jineng_shuju: None,
                });
            }
        }

        // Redis中没有数据，从MySQL查询
        let jiben_xinxi = match self.chaxun_jiben_xinxi(jineng_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        let huizong_xinxi = match self.chaxun_huizong_xinxi(jineng_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        // 检查是否至少有一个表有数据
        if jiben_xinxi.is_none() && huizong_xinxi.is_none() {
            return Ok(jineng_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_jineng_bucunzai(jineng_id)),
                wanzheng_xinxi: None,
                jineng_shuju: None,
            });
        }

        let wanzheng_xinxi = jineng_wanzheng_xinxi {
            id: jineng_id.to_string(),
            jiben_xinxi,
            huizong_xinxi,
        };

        // 如果有Redis控制器，将查询结果存储到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let _ = redis_kongzhi.cunchu_quanbu_xinxi(jineng_id, &wanzheng_xinxi).await;
        }

        Ok(jineng_chaxun_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            wanzheng_xinxi: Some(wanzheng_xinxi),
            jineng_shuju: None,
        })
    }

    /// 查询指定字段（只查询汇总表）
    async fn chaxun_zhiding_ziduan(&self, jineng_id: &str, ziduan_liebiao: &str) -> anyhow::Result<jineng_chaxun_jieguo> {
        let ziduan_mingcheng: Vec<&str> = ziduan_liebiao.split(',').map(|s| s.trim()).collect();

        // 验证字段是否都在汇总表中
        let huizong_biao_ziduan = jineng_ziduan_yingshe::huoqu_huizong_biao_ziduan();
        let mut youxiao_ziduan = Vec::new();

        for ziduan in &ziduan_mingcheng {
            if huizong_biao_ziduan.contains(&ziduan.to_string()) {
                youxiao_ziduan.push(*ziduan);
            }
        }

        if youxiao_ziduan.is_empty() {
            return Ok(jineng_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::cuowu_ziduan_bu_zai_huizong_biao.to_string()),
                wanzheng_xinxi: None,
                jineng_shuju: None,
            });
        }

        // 只查询汇总表字段
        match self.chaxun_huizong_biao_ziduan(jineng_id, &youxiao_ziduan).await {
            Ok(huizong_shuju) => {
                if huizong_shuju.is_empty() {
                    Ok(jineng_chaxun_jieguo {
                        chenggong: false,
                        cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_jineng_zai_huizong_biao_bucunzai(jineng_id)),
                        wanzheng_xinxi: None,
                        jineng_shuju: None,
                    })
                } else {
                    Ok(jineng_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        wanzheng_xinxi: None,
                        jineng_shuju: Some(huizong_shuju),
                    })
                }
            }
            Err(_) => {
                Ok(jineng_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(jineng_zifuchuan_changliangguanli::shengcheng_cuowu_jineng_zai_huizong_biao_bucunzai(jineng_id)),
                    wanzheng_xinxi: None,
                    jineng_shuju: None,
                })
            }
        }
    }

    /// 查询skill_name表基础信息
    async fn chaxun_jiben_xinxi(&self, jineng_id: &str) -> anyhow::Result<jineng_jiben_xinxi> {
        let row = sqlx::query(jineng_sql_guanli::sql_chaxun_jiben_xinxi)
            .bind(jineng_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                Ok(jineng_jiben_xinxi {
                    id: row.try_get::<Option<i32>, _>("ID").unwrap_or(None),
                    aegis_name: row.try_get::<Option<String>, _>("Aegis_name").unwrap_or(None),
                    name: row.try_get::<Option<String>, _>("name").unwrap_or(None),
                    schinese: row.try_get::<Option<String>, _>("schinese").unwrap_or(None),
                    tchinese: row.try_get::<Option<String>, _>("tchinese").unwrap_or(None),
                    jp: row.try_get::<Option<String>, _>("jp").unwrap_or(None),
                    kr: row.try_get::<Option<String>, _>("kr").unwrap_or(None),
                })
            }
            None => Err(anyhow::anyhow!(jineng_zifuchuan_changliangguanli::cuowu_jineng_jiben_xinxi_bucunzai))
        }
    }

    /// 查询jineng_huizong表汇总信息
    async fn chaxun_huizong_xinxi(&self, jineng_id: &str) -> anyhow::Result<jineng_huizong_xinxi> {
        let row = sqlx::query(jineng_sql_guanli::sql_chaxun_huizong_xinxi)
            .bind(jineng_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                Ok(jineng_huizong_xinxi {
                    jineng_id: row.try_get::<Option<i32>, _>("jineng_id").unwrap_or(None),
                    jineng_mingcheng: row.try_get::<Option<String>, _>("jineng_mingcheng").unwrap_or(None),
                    wupin_shuliang: row.try_get::<Option<i32>, _>("wupin_shuliang").unwrap_or(None),
                    zhiye_shuliang: row.try_get::<Option<i32>, _>("zhiye_shuliang").unwrap_or(None),
                    lishibiandong_shuliang: row.try_get::<Option<i32>, _>("lishibiandong_shuliang").unwrap_or(None),
                    zhongwenming: row.try_get::<Option<String>, _>("zhongwenming").unwrap_or(None),
                    leiming: row.try_get::<Option<String>, _>("leiming").unwrap_or(None),
                    tupian_url: row.try_get::<Option<String>, _>("tupian_url").unwrap_or(None),
                    zuida_dengji: row.try_get::<Option<i32>, _>("zuida_dengji").unwrap_or(None),
                    jichuxinxi_yaml: row.try_get::<Option<String>, _>("jichuxinxi_yaml").unwrap_or(None),
                    wupin_yaml: row.try_get::<Option<String>, _>("wupin_yaml").unwrap_or(None),
                    zhiye_yaml: row.try_get::<Option<String>, _>("zhiye_yaml").unwrap_or(None),
                    lishibiandong_yaml: row.try_get::<Option<String>, _>("lishibiandong_yaml").unwrap_or(None),
                    you_jichuxinxi: row.try_get::<Option<String>, _>("you_jichuxinxi").unwrap_or(None),
                    you_wupin: row.try_get::<Option<String>, _>("you_wupin").unwrap_or(None),
                    you_zhiye: row.try_get::<Option<String>, _>("you_zhiye").unwrap_or(None),
                    you_lishibiandong: row.try_get::<Option<String>, _>("you_lishibiandong").unwrap_or(None),
                })
            }
            None => Err(anyhow::anyhow!(jineng_zifuchuan_changliangguanli::cuowu_jineng_huizong_xinxi_bucunzai))
        }
    }

    /// 查询skill_name表指定字段
    async fn chaxun_jiben_biao_ziduan(&self, jineng_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = jineng_sql_guanli::shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(jineng_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 查询jineng_huizong表指定字段
    async fn chaxun_huizong_biao_ziduan(&self, jineng_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = jineng_sql_guanli::shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(jineng_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 批量查询技能
    pub async fn piliang_chaxun(&self, id_liebiao: Vec<String>, chaxun_moshi: &str) -> anyhow::Result<Vec<jineng_chaxun_jieguo>> {
        let mut jieguo_liebiao = Vec::new();

        for id in id_liebiao {
            let jieguo = self.tongyong_chaxun(&id, chaxun_moshi).await?;
            jieguo_liebiao.push(jieguo);
        }

        Ok(jieguo_liebiao)
    }

    /// 检查技能是否存在
    pub async fn jiancha_jineng_cunzai(&self, jineng_id: &str) -> anyhow::Result<bool> {
        // 检查skill_name表
        let jiben_jieguo = sqlx::query(jineng_sql_guanli::sql_jiancha_jiben_biao_cunzai)
            .bind(jineng_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let jiben_count: i64 = jiben_jieguo.get("count");
        if jiben_count > 0 {
            return Ok(true);
        }

        // 检查jineng_huizong表
        let huizong_jieguo = sqlx::query(jineng_sql_guanli::sql_jiancha_huizong_biao_cunzai)
            .bind(jineng_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let huizong_count: i64 = huizong_jieguo.get("count");
        Ok(huizong_count > 0)
    }

    /// 获取支持的字段列表
    /// 注意：指定字段查询只支持汇总表字段，全部信息查询支持两个表的字段
    pub fn huoqu_zhichi_ziduan() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert(jineng_zifuchuan_changliangguanli::biao_ming_skill_name.to_string(), jineng_ziduan_yingshe::huoqu_jiben_biao_ziduan());
        ziduan_map.insert(jineng_zifuchuan_changliangguanli::biao_ming_jineng_huizong.to_string(), jineng_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map.insert(jineng_zifuchuan_changliangguanli::biao_ming_zhiding_ziduan_zhichi.to_string(), jineng_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 清理技能全部数据获取的Redis缓存
    /// 只清除技能数据获取相关的缓存，不会清理其他缓存
    pub async fn qingchu_jineng_quanbu_xinxi_huancun(&self) -> anyhow::Result<u64> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.qingchu_jineng_quanbu_xinxi_huancun().await
            }
            None => Ok(0) // 没有Redis控制器，返回0
        }
    }

    /// 删除指定技能的全部信息缓存
    pub async fn shanchu_jineng_huancun(&self, jineng_id: &str) -> anyhow::Result<bool> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.shanchu_quanbu_xinxi(jineng_id).await
            }
            None => Ok(false) // 没有Redis控制器，返回false
        }
    }

    /// 获取技能缓存统计信息
    pub async fn huoqu_jineng_huancun_tongji(&self) -> anyhow::Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.huoqu_jineng_huancun_tongji().await
            }
            None => Ok(jineng_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }
}
