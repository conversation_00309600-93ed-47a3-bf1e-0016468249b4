#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_rizhi_kongzhi::jineng_zifuchuan_changliangguanli;
use super::jinengshujujiegouti::{jineng_liebiao_jieguo, jineng_wanzheng_xinxi};
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 技能Redis缓存控制类
pub struct jineng_redis_kongzhi {
    pub redis_lianjie: redis_lianjie_guanli,
}

impl jineng_redis_kongzhi {
    /// 创建新的技能Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 生成技能全部信息的Redis键名
    fn shengcheng_quanbu_xinxi_jian(&self, jineng_id: &str) -> String {
        jineng_zifuchuan_changliangguanli::shengcheng_redis_jian_jineng_quanbu(jineng_id)
    }

    /// 从Redis获取技能全部信息
    pub async fn huoqu_quanbu_xinxi(&self, jineng_id: &str) -> Result<Option<jineng_wanzheng_xinxi>> {
        let jian = self.shengcheng_quanbu_xinxi_jian(jineng_id);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<jineng_wanzheng_xinxi>(&json_str) {
                    Ok(jineng_xinxi) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &jineng_zifuchuan_changliangguanli::rizhi_redis_huoqu_chenggong_quanbu.replace("{}", jineng_id),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(Some(jineng_xinxi))
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("技能{}的Redis缓存JSON解析失败: {}", jineng_id, e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_wu_huancun_quanbu.replace("{}", jineng_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(None)
            }
        }
    }

    /// 将技能全部信息存储到Redis（3天缓存）
    pub async fn cunchu_quanbu_xinxi(&self, jineng_id: &str, jineng_xinxi: &jineng_wanzheng_xinxi) -> Result<()> {
        let jian = self.shengcheng_quanbu_xinxi_jian(jineng_id);
        let json_str = serde_json::to_string(jineng_xinxi)?;

        match self.redis_lianjie.shezhi_with_guoqi(
            &jian,
            &json_str,
            jineng_zifuchuan_changliangguanli::jineng_quanbu_xinxi_huancun_shijian as i64,
        ).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_cunchu_chenggong_quanbu.replace("{}", jineng_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("技能{}的全部信息缓存到Redis失败: {}", jineng_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 删除指定技能的全部信息缓存
    pub async fn shanchu_quanbu_xinxi(&self, jineng_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(jineng_id);

        match self.redis_lianjie.shanchu(&jian).await {
            Ok(shanchu_chenggong) => {
                if shanchu_chenggong {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &jineng_zifuchuan_changliangguanli::rizhi_redis_shanchu_chenggong_quanbu.replace("{}", jineng_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                    Ok(true)
                } else {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &jineng_zifuchuan_changliangguanli::rizhi_redis_wuxu_shanchu_quanbu.replace("{}", jineng_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                    Ok(false)
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("删除技能{}的Redis缓存失败: {}", jineng_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 清理技能全部数据获取的Redis缓存
    /// 只清除技能数据获取相关的缓存，不会清理其他缓存
    pub async fn qingchu_jineng_quanbu_xinxi_huancun(&self) -> Result<u64> {
        let moshi = jineng_zifuchuan_changliangguanli::redis_jian_moshi_jineng_quanbu;

        match self.redis_lianjie.shanchu_by_pattern(moshi).await {
            Ok(shanchu_shuliang) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_qingchu_chenggong_quanbu.replace("{}", &shanchu_shuliang.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(shanchu_shuliang)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_qingchu_shibai_quanbu.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 获取技能缓存统计信息
    pub async fn huoqu_jineng_huancun_tongji(&self) -> Result<String> {
        let moshi = jineng_zifuchuan_changliangguanli::redis_jian_moshi_jineng_quanbu;

        match self.redis_lianjie.count_keys_by_pattern(moshi).await {
            Ok(shuliang) => {
                let tongji_xinxi = jineng_zifuchuan_changliangguanli::shengcheng_tongji_jineng_quanbu_huancun(shuliang);
                Ok(tongji_xinxi)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_tongji_shibai.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    // ==================== 技能列表缓存管理 ====================

    /// 生成技能列表的Redis键名
    fn shengcheng_liebiao_jian(&self, meiye_shuliang: u32, dangqian_ye: u32) -> String {
        jineng_zifuchuan_changliangguanli::shengcheng_redis_jian_jineng_liebiao(meiye_shuliang, dangqian_ye)
    }

    /// 从Redis获取技能列表
    pub async fn huoqu_jineng_liebiao(&self, meiye_shuliang: u32, dangqian_ye: u32) -> Result<Option<jineng_liebiao_jieguo>> {
        let jian = self.shengcheng_liebiao_jian(meiye_shuliang, dangqian_ye);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<jineng_liebiao_jieguo>(&json_str) {
                    Ok(liebiao_jieguo) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("从Redis成功获取技能列表，每页{}个，第{}页", meiye_shuliang, dangqian_ye),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(Some(liebiao_jieguo))
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &jineng_zifuchuan_changliangguanli::rizhi_redis_json_jiexi_shibai_liebiao.replace("{}", &e.to_string()),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("技能列表在Redis中无缓存，每页{}个，第{}页", meiye_shuliang, dangqian_ye),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(None)
            }
        }
    }

    /// 将技能列表存储到Redis（5小时缓存）
    pub async fn cunchu_jineng_liebiao(&self, meiye_shuliang: u32, dangqian_ye: u32, liebiao_jieguo: &jineng_liebiao_jieguo) -> Result<()> {
        let jian = self.shengcheng_liebiao_jian(meiye_shuliang, dangqian_ye);
        let json_str = serde_json::to_string(liebiao_jieguo)?;

        match self.redis_lianjie.shezhi_with_guoqi(
            &jian,
            &json_str,
            jineng_zifuchuan_changliangguanli::jineng_liebiao_huancun_shijian as i64,
        ).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("技能列表已缓存到Redis，每页{}个，第{}页，有效期5小时", meiye_shuliang, dangqian_ye),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_cunchu_shibai_liebiao.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 清理技能列表的Redis缓存
    /// 只清除技能列表相关的缓存，不会清理其他缓存
    pub async fn qingchu_jineng_liebiao_huancun(&self) -> Result<u64> {
        let moshi = jineng_zifuchuan_changliangguanli::redis_jian_moshi_jineng_liebiao;

        match self.redis_lianjie.shanchu_by_pattern(moshi).await {
            Ok(shanchu_shuliang) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_qingchu_chenggong_liebiao.replace("{}", &shanchu_shuliang.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(shanchu_shuliang)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &jineng_zifuchuan_changliangguanli::rizhi_redis_qingchu_shibai_liebiao.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }
}
