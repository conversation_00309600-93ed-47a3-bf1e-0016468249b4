#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::jineng_liebiao_shuju::jineng_liebiao_guanli;
use super::jineng_redis_kongzhi::jineng_redis_kongzhi;
use super::jinengshujujiegouti::jineng_liebiao_fenye_canshu;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;

/// 技能列表功能测试类
pub struct jineng_liebiao_ceshi;

impl jineng_liebiao_ceshi {
    /// 完整的技能列表功能测试
    pub async fn wanzheng_jineng_liebiao_ceshi(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: Option<redis_lianjie_guanli>,
    ) -> anyhow::Result<()> {
        println!("{}", "=".repeat(80));
        println!("开始完整的技能列表功能测试");
        println!("{}", "=".repeat(80));

        // 创建技能列表管理器
        let liebiao_guanli = match redis_guanli {
            Some(redis) => {
                println!("✓ 使用Redis缓存模式创建技能列表管理器");
                let redis_kongzhi = jineng_redis_kongzhi::new(redis);
                jineng_liebiao_guanli::new_with_redis(mysql_guanli, redis_kongzhi)
            }
            None => {
                println!("✓ 使用无缓存模式创建技能列表管理器");
                jineng_liebiao_guanli::new(mysql_guanli)
            }
        };

        // 测试1: 基础分页查询测试
        println!("\n{}", "=".repeat(60));
        println!("测试1: 基础分页查询测试");
        println!("{}", "=".repeat(60));

        Self::ceshi_jiben_fenye_chaxun(&liebiao_guanli).await?;

        // 测试2: Redis缓存性能测试
        println!("\n{}", "=".repeat(60));
        println!("测试2: Redis缓存性能测试");
        println!("{}", "=".repeat(60));

        Self::ceshi_redis_huancun_xingneng(&liebiao_guanli).await?;

        // 测试3: 缓存管理测试
        println!("\n{}", "=".repeat(60));
        println!("测试3: 缓存管理测试");
        println!("{}", "=".repeat(60));

        Self::ceshi_huancun_guanli(&liebiao_guanli).await?;

        // 测试4: 边界条件测试
        println!("\n{}", "=".repeat(60));
        println!("测试4: 边界条件测试");
        println!("{}", "=".repeat(60));

        Self::ceshi_bianjie_tiaojian(&liebiao_guanli).await?;

        println!("\n{}", "=".repeat(80));
        println!("完整的技能列表功能测试完成！");
        println!("{}", "=".repeat(80));

        Ok(())
    }

    /// 测试基础分页查询
    async fn ceshi_jiben_fenye_chaxun(liebiao_guanli: &jineng_liebiao_guanli) -> anyhow::Result<()> {
        println!("子测试1.1: 获取第一页技能列表（每页10个）");
        let fenye_canshu1 = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 10,
            dangqian_ye: 1,
        };

        let kaishi_shijian = std::time::Instant::now();
        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu1).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                println!("✓ 查询耗时: {:?}", yongshi);
                println!("✓ 查询成功: {}", jieguo.chenggong);
                if let Some(cuowu) = &jieguo.cuowu_xinxi {
                    println!("✗ 错误信息: {}", cuowu);
                }
                println!("✓ 技能列表数量: {}", jieguo.jineng_liebiao.len());
                println!("✓ 分页信息:");
                println!("  - 当前页数: {}", jieguo.fenye_xinxi.dangqian_ye);
                println!("  - 总页数: {}", jieguo.fenye_xinxi.zongyeshu);
                println!("  - 总技能数: {}", jieguo.fenye_xinxi.zong_jineng_shu);
                println!("  - 当前页技能数: {}", jieguo.fenye_xinxi.dangqianye_jineng_shu);

                if !jieguo.jineng_liebiao.is_empty() {
                    println!("✓ 前3个技能详情:");
                    for (i, jineng) in jieguo.jineng_liebiao.iter().take(3).enumerate() {
                        println!("  {}. ID: {}, 名称: '{}', 类名: '{}'",
                                 i + 1,
                                 jineng.jineng_id,
                                 jineng.jineng_mingcheng,
                                 jineng.leiming.as_deref().unwrap_or("无")
                        );
                    }
                }

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 获取第一页技能列表失败: {}", e);
            }
        }

        println!("\n子测试1.2: 获取第二页技能列表（每页5个）");
        let fenye_canshu2 = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 5,
            dangqian_ye: 2,
        };

        let kaishi_shijian = std::time::Instant::now();
        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu2).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                println!("✓ 查询耗时: {:?}", yongshi);
                println!("✓ 查询成功: {}", jieguo.chenggong);
                println!("✓ 技能列表数量: {}", jieguo.jineng_liebiao.len());
                println!("✓ 分页信息: 第{}页/共{}页，总{}个技能，当前页{}个技能",
                         jieguo.fenye_xinxi.dangqian_ye,
                         jieguo.fenye_xinxi.zongyeshu,
                         jieguo.fenye_xinxi.zong_jineng_shu,
                         jieguo.fenye_xinxi.dangqianye_jineng_shu
                );

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 获取第二页技能列表失败: {}", e);
            }
        }

        Ok(())
    }

    /// 测试Redis缓存性能
    async fn ceshi_redis_huancun_xingneng(liebiao_guanli: &jineng_liebiao_guanli) -> anyhow::Result<()> {
        let fenye_canshu = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 8,
            dangqian_ye: 1,
        };

        println!("子测试2.1: 第一次查询（从数据库获取，建立缓存）");
        let kaishi_shijian1 = std::time::Instant::now();
        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu.clone()).await {
            Ok(jieguo) => {
                let yongshi1 = kaishi_shijian1.elapsed();
                println!("✓ 第一次查询耗时: {:?}", yongshi1);
                println!("✓ 查询成功: {}", jieguo.chenggong);
                println!("✓ 获取到{}个技能", jieguo.jineng_liebiao.len());

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 第一次查询失败: {}", e);
            }
        }

        println!("\n子测试2.2: 第二次查询（从Redis缓存获取）");
        let kaishi_shijian2 = std::time::Instant::now();
        match liebiao_guanli.huoqu_jineng_liebiao(fenye_canshu).await {
            Ok(jieguo) => {
                let yongshi2 = kaishi_shijian2.elapsed();
                println!("✓ 第二次查询耗时: {:?}", yongshi2);
                println!("✓ 查询成功: {}", jieguo.chenggong);
                println!("✓ 获取到{}个技能", jieguo.jineng_liebiao.len());

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 第二次查询失败: {}", e);
            }
        }

        Ok(())
    }

    /// 测试缓存管理功能
    async fn ceshi_huancun_guanli(liebiao_guanli: &jineng_liebiao_guanli) -> anyhow::Result<()> {
        println!("子测试3.1: 获取缓存统计信息");
        match liebiao_guanli.huoqu_liebiao_huancun_tongji().await {
            Ok(tongji) => {
                println!("✓ 缓存统计信息获取成功");
                println!("原始完整返回结果:");
                println!("{:#?}", tongji);
            }
            Err(e) => {
                println!("✗ 获取缓存统计信息失败: {}", e);
            }
        }

        println!("\n子测试3.2: 清理技能列表缓存");
        match liebiao_guanli.qingchu_jineng_liebiao_huancun().await {
            Ok(shanchu_shuliang) => {
                println!("✓ 缓存清理成功");
                println!("✓ 清理了{}个缓存项", shanchu_shuliang);
                println!("原始完整返回结果: {}", shanchu_shuliang);
            }
            Err(e) => {
                println!("✗ 清理缓存失败: {}", e);
            }
        }

        println!("\n子测试3.3: 清理后再次获取缓存统计信息");
        match liebiao_guanli.huoqu_liebiao_huancun_tongji().await {
            Ok(tongji) => {
                println!("✓ 清理后缓存统计信息获取成功");
                println!("原始完整返回结果:");
                println!("{:#?}", tongji);
            }
            Err(e) => {
                println!("✗ 获取清理后缓存统计信息失败: {}", e);
            }
        }

        Ok(())
    }

    /// 测试边界条件
    async fn ceshi_bianjie_tiaojian(liebiao_guanli: &jineng_liebiao_guanli) -> anyhow::Result<()> {
        println!("子测试4.1: 测试无效参数（每页0个）");
        let wuxiao_canshu1 = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 0,
            dangqian_ye: 1,
        };

        match liebiao_guanli.huoqu_jineng_liebiao(wuxiao_canshu1).await {
            Ok(jieguo) => {
                println!("✓ 无效参数处理正确");
                println!("✓ 查询成功: {}", jieguo.chenggong);
                if let Some(cuowu) = &jieguo.cuowu_xinxi {
                    println!("✓ 错误信息: {}", cuowu);
                }

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 无效参数测试失败: {}", e);
            }
        }

        println!("\n子测试4.2: 测试无效参数（第0页）");
        let wuxiao_canshu2 = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 10,
            dangqian_ye: 0,
        };

        match liebiao_guanli.huoqu_jineng_liebiao(wuxiao_canshu2).await {
            Ok(jieguo) => {
                println!("✓ 无效页数处理正确");
                println!("✓ 查询成功: {}", jieguo.chenggong);
                if let Some(cuowu) = &jieguo.cuowu_xinxi {
                    println!("✓ 错误信息: {}", cuowu);
                }

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 无效页数测试失败: {}", e);
            }
        }

        println!("\n子测试4.3: 测试超大页数");
        let chaoda_canshu = jineng_liebiao_fenye_canshu {
            meiye_shuliang: 10,
            dangqian_ye: 99999,
        };

        match liebiao_guanli.huoqu_jineng_liebiao(chaoda_canshu).await {
            Ok(jieguo) => {
                println!("✓ 超大页数处理正确");
                println!("✓ 查询成功: {}", jieguo.chenggong);
                if let Some(cuowu) = &jieguo.cuowu_xinxi {
                    println!("✓ 错误信息: {}", cuowu);
                }

                println!("原始完整返回结果:");
                println!("{:#?}", jieguo);
            }
            Err(e) => {
                println!("✗ 超大页数测试失败: {}", e);
            }
        }

        Ok(())
    }
}
