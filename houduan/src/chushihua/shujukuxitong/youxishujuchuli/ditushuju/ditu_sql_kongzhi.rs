#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 地图数据SQL控制类
pub struct ditu_sql_kongzhi;

impl ditu_sql_kongzhi {
    /// 特殊字段名：获取全部信息
    pub const quanbu_xinxi_ziduan: &'static str = "quanbu_xinxi";

    /// 查询ditu_huizong表单个字段的SQL模板
    pub fn huoqu_huizong_ziduan_sql() -> &'static str {
        "SELECT {} FROM ditu_huizong WHERE ditu_id = ?"
    }

    /// 查询ditu_huizong表全部字段的SQL
    pub fn huoqu_huizong_quanbu_sql() -> &'static str {
        "SELECT * FROM ditu_huizong WHERE ditu_id = ?"
    }

    /// 查询ditu_name表全部字段的SQL
    pub fn huoqu_name_quanbu_sql() -> &'static str {
        "SELECT * FROM ditu_name WHERE id = ?"
    }

    /// 检查字段是否存在于ditu_huizong表的SQL
    pub fn jiancha_ziduan_cunzai_sql() -> &'static str {
        "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'xianjingziliaozhan' AND TABLE_NAME = 'ditu_huizong' AND COLUMN_NAME = ?"
    }

    /// 构建动态字段查询SQL
    pub fn gouzao_ziduan_chaxun_sql(ziduan_ming: &str) -> String {
        format!("SELECT {} FROM ditu_huizong WHERE ditu_id = ?", ziduan_ming)
    }

    /// 验证字段名是否安全（防止SQL注入）
    pub fn yanzheng_ziduan_ming_anquan(ziduan_ming: &str) -> bool {
        // 只允许字母、数字、下划线
        ziduan_ming.chars().all(|c| c.is_alphanumeric() || c == '_')
    }

    /// 获取ditu_huizong表的所有字段名列表
    pub fn huoqu_huizong_suoyou_ziduan() -> Vec<&'static str> {
        vec![
            "ditu_id", "ditu_mingcheng", "xianshi_mingcheng",
            "leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
            "leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
            "fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
            "fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi",
            "you_guaiwu", "you_npc", "you_yinyue", "you_tupian", "tongzhi_jinru",
            "guaiwu_zongshu", "npc_zongshu", "tupian_shuliang",
            "xinxi_biaoti", "fu_biaoti", "zhu_biaoti", "beijing_tupian", "yinyue_wenjian",
            "riqi", "zhu_fenlei", "suoyou_fenlei", "liebiao_fenlei",
            "jichuxinxi_yaml", "guaiwu_liebiao_yaml", "npc_liebiao_yaml",
            "ditu_tupian_yaml", "benditupian_lujing_yaml",
            "you_jichuxinxi", "you_guaiwuxinxi", "you_npcxinxi", "huizong_riqi"
        ]
    }

    /// 检查字段名是否在允许的字段列表中
    pub fn jiancha_ziduan_hefa(ziduan_ming: &str) -> bool {
        if ziduan_ming == Self::quanbu_xinxi_ziduan {
            return true;
        }
        Self::huoqu_huizong_suoyou_ziduan().contains(&ziduan_ming)
    }
}