#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_fenlei_chaxun::ditu_fenlei_chaxun;
use super::ditushujujiegouti::{ditu_fenlei_chaxun_canshu, ditu_fenlei_tiaojian};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use std::fs::OpenOptions;
use std::io::Write;

/// 地图分类查询测试类
pub struct ditu_fenlei_ceshi;

impl ditu_fenlei_ceshi {
    /// 完整的地图分类查询功能测试
    pub async fn wanzheng_fenlei_ceshi(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: Option<redis_lianjie_guanli>,
    ) -> anyhow::Result<()> {
        let mut log_neirong = String::new();

        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));
        Self::tianjia_log(&mut log_neirong, "开始完整的地图分类查询功能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));

        // 创建分类查询管理器
        let fenlei_chaxun = match redis_guanli {
            Some(redis) => {
                Self::tianjia_log(&mut log_neirong, "✓ 使用Redis缓存模式创建地图分类查询管理器");
                ditu_fenlei_chaxun::new_with_redis(mysql_guanli, redis)
            }
            None => {
                Self::tianjia_log(&mut log_neirong, "✓ 使用无缓存模式创建地图分类查询管理器");
                ditu_fenlei_chaxun::new(mysql_guanli)
            }
        };

        // 测试1: 所有类型字段测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试1: 所有类型字段测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_suoyou_leixing_ziduan(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试2: 所有分类字段测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试2: 所有分类字段测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_suoyou_fenlei_ziduan(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试3: 多个分类条件AND查询
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试3: 多个分类条件AND查询");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_duoge_fenlei_and_chaxun(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试4: 多个分类条件OR查询
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试4: 多个分类条件OR查询");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_duoge_fenlei_or_chaxun(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试5: Redis缓存性能测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试5: Redis缓存性能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_redis_huancun_xingneng(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试6: 缓存清除功能测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试6: 缓存清除功能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_huancun_qingchu(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试7: 边界条件测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试7: 边界条件测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_bianjie_tiaojian(&fenlei_chaxun, &mut log_neirong).await?;

        // 最终清除所有测试产生的缓存
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "最终清理: 清除所有测试产生的缓存");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        match fenlei_chaxun.qingchu_fenlei_huancun().await {
            Ok(shuliang) => {
                Self::tianjia_log(&mut log_neirong, &format!("✓ 最终清除{}个测试缓存", shuliang));
            }
            Err(e) => {
                Self::tianjia_log(&mut log_neirong, &format!("✗ 最终清除缓存失败: {}", e));
            }
        }

        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(80)));
        Self::tianjia_log(&mut log_neirong, "完整的地图分类查询功能测试完成！");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));

        // 保存日志到文件
        Self::baocun_log_wenjian(&log_neirong).await?;

        // 同时输出到控制台
        println!("{}", log_neirong);

        Ok(())
    }

    /// 测试所有类型字段
    async fn ceshi_suoyou_leixing_ziduan(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        let leixing_ziduan = vec![
            ("leixing_town", "城镇类型"),
            ("leixing_field", "野外类型"),
            ("leixing_dungeon", "地下城类型"),
            ("leixing_quest", "任务类型"),
            ("leixing_instance", "副本类型"),
            ("leixing_siege", "攻城类型"),
            ("leixing_pvp", "PVP类型"),
            ("leixing_other", "其他类型"),
            ("leixing_weizhi", "位置类型"),
        ];

        for (i, (ziduan_ming, miaoshu)) in leixing_ziduan.iter().enumerate() {
            Self::tianjia_log(log_neirong, &format!("子测试1.{}: 查询{}地图（{}=true）", i + 1, miaoshu, ziduan_ming));

            let tiaojian = vec![ditu_fenlei_tiaojian::new(ziduan_ming.to_string(), true)];
            let canshu = ditu_fenlei_chaxun_canshu::new(3, 1, tiaojian, "AND".to_string());

            let kaishi_shijian = std::time::Instant::now();
            match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
                Ok(jieguo) => {
                    let yongshi = kaishi_shijian.elapsed();
                    Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                    Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个{}地图", jieguo.zonggong_ditu_shuliang, miaoshu));
                    Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                    if jieguo.dangqian_ye_ditu_shuliang > 0 {
                        Self::tianjia_log(log_neirong, "前3个地图示例:");
                        for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(3).enumerate() {
                            Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                        }
                    } else {
                        Self::tianjia_log(log_neirong, "  无符合条件的地图");
                    }
                }
                Err(e) => {
                    Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
                }
            }
            Self::tianjia_log(log_neirong, "");
        }

        Ok(())
    }

    /// 测试所有分类字段
    async fn ceshi_suoyou_fenlei_ziduan(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        let fenlei_ziduan = vec![
            ("fenlei_town", "城镇分类"),
            ("fenlei_field", "野外分类"),
            ("fenlei_dungeon", "地下城分类"),
            ("fenlei_quest", "任务分类"),
            ("fenlei_instance", "副本分类"),
            ("fenlei_siege", "攻城分类"),
            ("fenlei_pvp", "PVP分类"),
            ("fenlei_other", "其他分类"),
            ("fenlei_weizhi", "未知分类"),
        ];

        for (i, (ziduan_ming, miaoshu)) in fenlei_ziduan.iter().enumerate() {
            Self::tianjia_log(log_neirong, &format!("子测试2.{}: 查询{}地图（{}=true）", i + 1, miaoshu, ziduan_ming));

            let tiaojian = vec![ditu_fenlei_tiaojian::new(ziduan_ming.to_string(), true)];
            let canshu = ditu_fenlei_chaxun_canshu::new(3, 1, tiaojian, "AND".to_string());

            let kaishi_shijian = std::time::Instant::now();
            match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
                Ok(jieguo) => {
                    let yongshi = kaishi_shijian.elapsed();
                    Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                    Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个{}地图", jieguo.zonggong_ditu_shuliang, miaoshu));
                    Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                    if jieguo.dangqian_ye_ditu_shuliang > 0 {
                        Self::tianjia_log(log_neirong, "前3个地图示例:");
                        for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(3).enumerate() {
                            Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                        }
                    } else {
                        Self::tianjia_log(log_neirong, "  无符合条件的地图");
                    }
                }
                Err(e) => {
                    Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
                }
            }
            Self::tianjia_log(log_neirong, "");
        }

        Ok(())
    }

    /// 测试多个分类条件AND查询
    async fn ceshi_duoge_fenlei_and_chaxun(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        // 测试组合1: 城镇类型 AND 城镇分类
        Self::tianjia_log(log_neirong, "子测试3.1: 查询城镇类型且城镇分类的地图（leixing_town=true AND fenlei_town=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_town".to_string(), true),
            ditu_fenlei_tiaojian::new("fenlei_town".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前5个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(5).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        // 测试组合2: 野外类型 AND 野外分类
        Self::tianjia_log(log_neirong, "\n子测试3.2: 查询野外类型且野外分类的地图（leixing_field=true AND fenlei_field=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_field".to_string(), true),
            ditu_fenlei_tiaojian::new("fenlei_field".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前5个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(5).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        // 测试组合3: 地下城类型 AND 地下城分类
        Self::tianjia_log(log_neirong, "\n子测试3.3: 查询地下城类型且地下城分类的地图（leixing_dungeon=true AND fenlei_dungeon=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_dungeon".to_string(), true),
            ditu_fenlei_tiaojian::new("fenlei_dungeon".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前5个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(5).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试多个分类条件OR查询
    async fn ceshi_duoge_fenlei_or_chaxun(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        // 测试组合1: 城镇类型 OR 地下城类型
        Self::tianjia_log(log_neirong, "子测试4.1: 查询城镇或地下城类型的地图（leixing_town=true OR leixing_dungeon=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_town".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_dungeon".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(8, 1, tiaojian, "OR".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前8个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(8).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        // 测试组合2: 野外类型 OR 副本类型 OR PVP类型
        Self::tianjia_log(log_neirong, "\n子测试4.2: 查询野外、副本或PVP类型的地图（leixing_field=true OR leixing_instance=true OR leixing_pvp=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_field".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_instance".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_pvp".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(6, 1, tiaojian, "OR".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前6个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(6).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        // 测试组合3: 任务类型 OR 攻城类型 OR 其他类型
        Self::tianjia_log(log_neirong, "\n子测试4.3: 查询任务、攻城或其他类型的地图（leixing_quest=true OR leixing_siege=true OR leixing_other=true）");

        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_quest".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_siege".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_other".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "OR".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));

                if jieguo.dangqian_ye_ditu_shuliang > 0 {
                    Self::tianjia_log(log_neirong, "前5个地图示例:");
                    for (idx, xiangmu) in jieguo.ditu_liebiao.iter().take(5).enumerate() {
                        Self::tianjia_log(log_neirong, &format!("  {}. {} - {}", idx + 1, xiangmu.ditu_id, xiangmu.ditu_mingzi));
                    }
                }
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试Redis缓存性能
    async fn ceshi_redis_huancun_xingneng(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        let tiaojian = vec![ditu_fenlei_tiaojian::new("leixing_field".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(6, 1, tiaojian, "AND".to_string());

        Self::tianjia_log(log_neirong, "子测试4.1: 第一次查询（从数据库获取，建立缓存）");
        let kaishi_shijian1 = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu.clone()).await {
            Ok(jieguo) => {
                let yongshi1 = kaishi_shijian1.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 第一次查询耗时: {:?}", yongshi1));
                Self::tianjia_log(log_neirong, &format!("✓ 获取到{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 第一次查询失败: {}", e));
            }
        }

        Self::tianjia_log(log_neirong, "\n子测试4.2: 第二次查询（从Redis缓存获取）");
        let kaishi_shijian2 = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi2 = kaishi_shijian2.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 第二次查询耗时: {:?}", yongshi2));
                Self::tianjia_log(log_neirong, &format!("✓ 获取到{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 第二次查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试缓存清除功能
    async fn ceshi_huancun_qingchu(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试5.1: 清除所有分类查询缓存");

        match fenlei_chaxun.qingchu_fenlei_huancun().await {
            Ok(shuliang) => {
                Self::tianjia_log(log_neirong, &format!("✓ 成功清除{}个分类查询缓存", shuliang));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 清除缓存失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试边界条件
    async fn ceshi_bianjie_tiaojian(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试6.1: 空条件查询（应该返回所有地图）");

        let tiaojian = vec![];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                Self::tianjia_log(log_neirong, &format!("✓ 空条件查询成功，总共{}个地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 空条件查询失败: {}", e));
            }
        }

        Self::tianjia_log(log_neirong, "\n子测试6.2: 无效字段名查询");

        let tiaojian = vec![ditu_fenlei_tiaojian::new("invalid_field".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                Self::tianjia_log(log_neirong, &format!("✓ 无效字段查询结果，总共{}个地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 无效字段查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 添加日志内容
    fn tianjia_log(log_neirong: &mut String, neirong: &str) {
        log_neirong.push_str(neirong);
        log_neirong.push('\n');
    }

    /// 保存日志到文件
    async fn baocun_log_wenjian(log_neirong: &str) -> anyhow::Result<()> {
        let riqi = chrono::Local::now().format("%Y-%m-%d").to_string();
        let shijian = chrono::Local::now().format("%H-%M-%S").to_string();
        let wenjian_ming = format!("rizhi/ditu_fenlei_ceshi_{}_{}.log", riqi, shijian);

        let mut wenjian = OpenOptions::new()
            .create(true)
            .write(true)
            .append(true)
            .open(&wenjian_ming)?;

        writeln!(wenjian, "{}", log_neirong)?;
        println!("✓ 测试日志已保存到: {}", wenjian_ming);

        Ok(())
    }
}
