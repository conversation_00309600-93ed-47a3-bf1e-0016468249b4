#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use super::ditu_sql_kongzhi::ditu_sql_kongzhi;
use super::ditushujujiegouti::{ditu_huancun_caozuo_jieguo, ditu_quanbu_xinxi_jieguo, ditu_ziduan_jieguo};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde_json;
use sqlx::{Column, Row};
use std::collections::HashMap;

/// 地图数据处理类
pub struct ditushujuchuli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditushujuchuli {
    /// 创建新的地图数据处理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis连接的地图数据处理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie.clone());
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 根据地图ID和字段名获取地图数据
    /// 如果字段名是"quanbu_xinxi"，则获取全部信息并使用Redis缓存
    /// 否则只获取单个字段，不使用缓存
    pub async fn huoqu_ditu_shuju(&self, ditu_id: &str, ziduan_ming: &str) -> anyhow::Result<serde_json::Value> {
        // 验证字段名
        if !ditu_sql_kongzhi::jiancha_ziduan_hefa(ziduan_ming) {
            ditu_rizhi_kongzhi::ziduan_ming_yanzheng_shibai(ziduan_ming);
            return Err(anyhow::anyhow!(ditu_rizhi_kongzhi::ziduan_ming_bu_hefa_cuowu(ziduan_ming)));
        }

        // 检查是否是获取全部信息
        if ziduan_ming == ditu_sql_kongzhi::quanbu_xinxi_ziduan {
            // 获取全部信息，使用Redis缓存
            match self.huoqu_ditu_quanbu_xinxi(ditu_id).await {
                Ok(Some(jieguo)) => Ok(serde_json::to_value(jieguo)?),
                Ok(None) => {
                    ditu_rizhi_kongzhi::ditu_shuju_bu_cunzai(ditu_id);
                    Ok(serde_json::Value::Null)
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_shibai(ditu_id, &e.to_string());
                    Err(e)
                }
            }
        } else {
            // 获取单个字段，不使用缓存
            match self.huoqu_ditu_ziduan(ditu_id, ziduan_ming).await {
                Ok(jieguo) => Ok(serde_json::to_value(jieguo)?),
                Err(e) => {
                    ditu_rizhi_kongzhi::huoqu_ditu_ziduan_shibai(ditu_id, ziduan_ming, &e.to_string());
                    Err(e)
                }
            }
        }
    }

    /// 获取地图单个字段数据（不使用缓存）
    pub async fn huoqu_ditu_ziduan(&self, ditu_id: &str, ziduan_ming: &str) -> anyhow::Result<ditu_ziduan_jieguo> {
        // 验证字段名安全性
        if !ditu_sql_kongzhi::yanzheng_ziduan_ming_anquan(ziduan_ming) {
            return Err(anyhow::anyhow!(ditu_rizhi_kongzhi::ziduan_ming_bu_anquan_cuowu(ziduan_ming)));
        }

        // 构建SQL查询
        let sql = ditu_sql_kongzhi::gouzao_ziduan_chaxun_sql(ziduan_ming);

        // 执行查询
        match sqlx::query(&sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let ziduan_zhi: Option<String> = row.try_get(ziduan_ming).unwrap_or(None);
                ditu_rizhi_kongzhi::huoqu_ditu_ziduan_chenggong(ditu_id, ziduan_ming);
                Ok(ditu_ziduan_jieguo::new(
                    ditu_id.to_string(),
                    ziduan_ming.to_string(),
                    ziduan_zhi,
                ))
            }
            Ok(None) => {
                ditu_rizhi_kongzhi::ditu_shuju_bu_cunzai(ditu_id);
                Ok(ditu_ziduan_jieguo::new(
                    ditu_id.to_string(),
                    ziduan_ming.to_string(),
                    None,
                ))
            }
            Err(e) => {
                ditu_rizhi_kongzhi::shujuku_chaxun_shibai(&sql, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 获取地图全部信息（使用Redis缓存）
    pub async fn huoqu_ditu_quanbu_xinxi(&self, ditu_id: &str) -> anyhow::Result<Option<ditu_quanbu_xinxi_jieguo>> {
        // 先尝试从Redis获取缓存数据
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(huancun_shuju)) = redis_kongzhi.huoqu_ditu_quanbu_xinxi(ditu_id).await {
                // 反序列化JSON数据
                if let Ok(shuju_map) = serde_json::from_str::<HashMap<String, String>>(&huancun_shuju) {
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(ditu_id, ditu_rizhi_kongzhi::shuju_laiyuan_huancun);
                    return Ok(Some(ditu_quanbu_xinxi_jieguo::new(
                        ditu_id.to_string(),
                        shuju_map,
                        ditu_rizhi_kongzhi::shuju_laiyuan_huancun.to_string(),
                    )));
                }
            }
        }

        // 从数据库获取数据
        let mut hebing_shuju = HashMap::new();

        // 获取ditu_huizong表数据
        let huizong_sql = ditu_sql_kongzhi::huoqu_huizong_quanbu_sql();
        match sqlx::query(huizong_sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                // 获取所有列名和值
                let lie_mingcheng = row.columns();
                for lie in lie_mingcheng {
                    let lie_ming = lie.name();
                    let zhi: Option<String> = row.try_get(lie_ming).unwrap_or(None);
                    hebing_shuju.insert(lie_ming.to_string(), zhi.unwrap_or_default());
                }
            }
            Ok(None) => {
                ditu_rizhi_kongzhi::ditu_shuju_bu_cunzai(ditu_id);
                return Ok(None);
            }
            Err(e) => {
                ditu_rizhi_kongzhi::shujuku_chaxun_shibai(huizong_sql, &e.to_string());
                return Err(e.into());
            }
        }

        // 获取ditu_name表数据
        let name_sql = ditu_sql_kongzhi::huoqu_name_quanbu_sql();
        match sqlx::query(name_sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                // 获取所有列名和值，添加前缀避免冲突
                let lie_mingcheng = row.columns();
                for lie in lie_mingcheng {
                    let lie_ming = lie.name();
                    let zhi: Option<String> = row.try_get(lie_ming).unwrap_or(None);
                    let qianzhui_lie_ming = if lie_ming == ditu_rizhi_kongzhi::id_ziduan_ming {
                        ditu_rizhi_kongzhi::name_biao_id_ziduan.to_string()
                    } else {
                        format!("{}{}", ditu_rizhi_kongzhi::name_biao_ziduan_qianzhui, lie_ming)
                    };
                    hebing_shuju.insert(qianzhui_lie_ming, zhi.unwrap_or_default());
                }
            }
            Ok(None) => {
                // name表数据不存在不影响主要数据
            }
            Err(e) => {
                ditu_rizhi_kongzhi::shujuku_chaxun_shibai(name_sql, &e.to_string());
                // name表查询失败不影响主要数据，继续处理
            }
        }

        // 将数据存储到Redis缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(json_shuju) = serde_json::to_string(&hebing_shuju) {
                if let Err(e) = redis_kongzhi.shezhi_ditu_quanbu_xinxi(ditu_id, &json_shuju).await {
                    ditu_rizhi_kongzhi::huancun_ditu_shuju_shibai(ditu_id, &e.to_string());
                } else {
                    ditu_rizhi_kongzhi::huancun_ditu_shuju_chenggong(ditu_id);
                }
            }
        }

        ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(ditu_id, ditu_rizhi_kongzhi::shuju_laiyuan_shujuku);
        Ok(Some(ditu_quanbu_xinxi_jieguo::new(
            ditu_id.to_string(),
            hebing_shuju,
            ditu_rizhi_kongzhi::shuju_laiyuan_shujuku.to_string(),
        )))
    }

    /// 清除指定地图的全部信息缓存
    pub async fn qingchu_ditu_huancun(&self, ditu_id: &str) -> anyhow::Result<ditu_huancun_caozuo_jieguo> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            match redis_kongzhi.shanchu_ditu_quanbu_xinxi(ditu_id).await {
                Ok(shanchu_chenggong) => {
                    if shanchu_chenggong {
                        ditu_rizhi_kongzhi::qingchu_ditu_huancun_chenggong(ditu_id);
                        Ok(ditu_huancun_caozuo_jieguo::chenggong(
                            ditu_id.to_string(),
                            ditu_rizhi_kongzhi::chenggong_qingchu_ditu_huancun_xiaoxi.to_string(),
                        ))
                    } else {
                        Ok(ditu_huancun_caozuo_jieguo::chenggong(
                            ditu_id.to_string(),
                            ditu_rizhi_kongzhi::ditu_huancun_bu_cunzai_xiaoxi.to_string(),
                        ))
                    }
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::qingchu_ditu_huancun_shibai(ditu_id, &e.to_string());
                    Ok(ditu_huancun_caozuo_jieguo::shibai(
                        ditu_id.to_string(),
                        ditu_rizhi_kongzhi::qingchu_ditu_huancun_shibai_xiaoxi(&e.to_string()),
                    ))
                }
            }
        } else {
            Ok(ditu_huancun_caozuo_jieguo::shibai(
                ditu_id.to_string(),
                ditu_rizhi_kongzhi::redis_wei_peizhi_wufa_qingchu_xiaoxi.to_string(),
            ))
        }
    }

    /// 检查指定地图的缓存是否存在
    pub async fn jiancha_ditu_huancun_cunzai(&self, ditu_id: &str) -> anyhow::Result<bool> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            redis_kongzhi.jiancha_ditu_huancun_cunzai(ditu_id).await
        } else {
            Ok(false)
        }
    }

    /// 获取地图缓存统计信息
    pub async fn huoqu_ditu_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            redis_kongzhi.huoqu_ditu_huancun_tongji().await
        } else {
            Ok(ditu_rizhi_kongzhi::redis_lianjie_wei_peizhi_xiaoxi.to_string())
        }
    }

    /// 清除所有地图相关的Redis缓存
    pub async fn qingchu_suoyou_ditu_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            redis_kongzhi.qingchu_suoyou_ditu_huancun().await
        } else {
            Err(anyhow::anyhow!(ditu_rizhi_kongzhi::redis_lianjie_wei_peizhi_xiaoxi))
        }
    }
}