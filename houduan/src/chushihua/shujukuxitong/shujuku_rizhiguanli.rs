use crate::rizhixitong;

/// 数据库系统信息日志
pub fn shujukuxitong_rizhi_xinxi(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("数据库系统模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 数据库系统警告日志
pub fn shujukuxitong_rizhi_jinggao(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("数据库系统模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
}

/// 数据库系统错误日志
pub fn shujukuxitong_rizhi_cuowu(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("数据库系统模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_cuowu(&rizhi_xiaoxi);
}

/// 数据库系统调试日志
pub fn shujukuxitong_rizhi_tiaoshi(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("数据库系统模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_tiaoshi(&rizhi_xiaoxi);
}

/// 兼容性函数：根据消息内容自动判断日志级别
/// 建议使用具体的级别函数替代此函数
pub fn shujukuxitong_rizhi(mokuai_ming: &str, xiaoxi: &str) {
    let xiaoxi_xiaoxie = xiaoxi.to_lowercase();

    // 判断是否为错误信息
    if xiaoxi_xiaoxie.contains("失败") || xiaoxi_xiaoxie.contains("错误") ||
       xiaoxi_xiaoxie.contains("异常") || xiaoxi_xiaoxie.contains("error") ||
       xiaoxi_xiaoxie.contains("failed") || xiaoxi_xiaoxie.contains("exception") {
        shujukuxitong_rizhi_cuowu(mokuai_ming, xiaoxi);
    }
    // 判断是否为警告信息
    else if xiaoxi_xiaoxie.contains("警告") || xiaoxi_xiaoxie.contains("warn") ||
            xiaoxi_xiaoxie.contains("注意") || xiaoxi_xiaoxie.contains("超时") {
        shujukuxitong_rizhi_jinggao(mokuai_ming, xiaoxi);
    }
    // 其他情况作为信息日志
    else {
        shujukuxitong_rizhi_xinxi(mokuai_ming, xiaoxi);
    }
}