use crate::chushihua::peizhi::zongpeizhi_peizhi::peizhixitong_redis_peizhi;
use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::shujukuxitong_rizhi;
use deadpool_redis::{Pool, Connection};
use redis::AsyncCommands;

#[derive(Clone)]
pub struct redis_lianjie_guanli {
    pub lianjiechi: Option<Pool>,
}

impl Default for redis_lianjie_guanli {
    fn default() -> Self {
        Self::new()
    }
}

impl redis_lianjie_guanli {
    pub fn new() -> Self {
        Self { lianjiechi: None }
    }

    pub fn lianjie(&mut self, peizhi: &peizhixitong_redis_peizhi) -> anyhow::Result<()> {
        let redis_url = format!("redis://:{}@{}:{}", peizhi.mima, peizhi.ip, peizhi.duankou);

        // 创建连接池配置，优化性能参数
        let mut cfg = deadpool_redis::Config::from_url(redis_url);

        // 设置连接池参数以优化性能
        cfg.pool = Some(deadpool_redis::PoolConfig {
            max_size: 50,                                    // 最大连接数（增加到50）
            timeouts: deadpool_redis::Timeouts {
                wait: Some(std::time::Duration::from_secs(60)),      // 获取连接超时时间（增加到60秒）
                create: Some(std::time::Duration::from_secs(15)),    // 创建连接超时时间
                recycle: Some(std::time::Duration::from_secs(10)),   // 连接回收超时时间
            },
            ..Default::default()
        });

        let pool = match cfg.create_pool(Some(deadpool_redis::Runtime::Tokio1)) {
            Ok(p) => p,
            Err(e) => {
                shujukuxitong_rizhi("redis", "创建连接池失败");
                return Err(e.into());
            }
        };

        self.lianjiechi = Some(pool);
        shujukuxitong_rizhi("redis", "Redis连接池配置：最大连接数50，获取连接超时60秒");
        Ok(())
    }

    /// 从连接池获取连接
    pub async fn huoqu_lianjie(&self) -> anyhow::Result<Connection> {
        match &self.lianjiechi {
            Some(pool) => match pool.get().await {
                Ok(conn) => Ok(conn),
                Err(e) => {
                    shujukuxitong_rizhi("redis", "获取连接失败");
                    Err(e.into())
                }
            },
            None => {
                let err_msg = "Redis连接池未初始化";
                shujukuxitong_rizhi("redis", "获取连接失败");
                Err(anyhow::anyhow!(err_msg))
            }
        }
    }

    /// 获取连接池引用（用于直接执行操作）
    pub fn huoqu_lianjiechi(&self) -> anyhow::Result<&Pool> {
        match &self.lianjiechi {
            Some(pool) => Ok(pool),
            None => {
                let err_msg = "Redis连接池未初始化";
                shujukuxitong_rizhi("redis", "获取连接池失败");
                Err(anyhow::anyhow!(err_msg))
            }
        }
    }

    /// 设置键值对
    pub async fn shezhi(&self, jian: &str, zhi: &str) -> anyhow::Result<()> {
        let mut conn = self.huoqu_lianjie().await?;
        if let Err(e) = conn.set::<&str, &str, ()>(jian, zhi).await {
            shujukuxitong_rizhi("redis", &format!("设置键 {} 失败", jian));
            return Err(e.into());
        }
        Ok(())
    }

    /// 获取值
    pub async fn huoqu(&self, jian: &str) -> anyhow::Result<Option<String>> {
        let mut conn = self.huoqu_lianjie().await?;
        match conn.get(jian).await {
            Ok(zhi) => Ok(zhi),
            Err(e) => {
                shujukuxitong_rizhi("redis", &format!("获取键 {} 失败", jian));
                Err(e.into())
            }
        }
    }

    /// 删除键
    pub async fn shanchu(&self, jian: &str) -> anyhow::Result<bool> {
        let mut conn = self.huoqu_lianjie().await?;
        match conn.del::<&str, usize>(jian).await {
            Ok(result) => Ok(result > 0),
            Err(e) => {
                shujukuxitong_rizhi("redis", &format!("删除键 {} 失败", jian));
                Err(e.into())
            }
        }
    }

    /// 检查键是否存在
    pub async fn cunzai(&self, jian: &str) -> anyhow::Result<bool> {
        let mut conn = self.huoqu_lianjie().await?;
        match conn.exists(jian).await {
            Ok(result) => Ok(result),
            Err(e) => {
                shujukuxitong_rizhi("redis", &format!("检查键 {} 是否存在失败", jian));
                Err(e.into())
            }
        }
    }

    /// 设置过期时间（秒）
    pub async fn shezhi_guoqi(&self, jian: &str, miao: i64) -> anyhow::Result<bool> {
        let mut conn = self.huoqu_lianjie().await?;
        match conn.expire::<&str, bool>(jian, miao).await {
            Ok(result) => Ok(result),
            Err(e) => {
                shujukuxitong_rizhi("redis", &format!("设置键 {} 过期时间失败", jian));
                Err(e.into())
            }
        }
    }

    /// 设置键值对并设置过期时间（批量操作，使用单个连接）
    pub async fn shezhi_with_guoqi(&self, jian: &str, zhi: &str, miao: i64) -> anyhow::Result<()> {
        let mut conn = self.huoqu_lianjie().await?;

        // 在同一个连接上执行两个操作
        if let Err(e) = conn.set::<&str, &str, ()>(jian, zhi).await {
            shujukuxitong_rizhi("redis", &format!("设置键 {} 失败", jian));
            return Err(e.into());
        }

        if let Err(e) = conn.expire::<&str, bool>(jian, miao).await {
            shujukuxitong_rizhi("redis", &format!("设置键 {} 过期时间失败", jian));
            return Err(e.into());
        }

        Ok(())
    }

    /// 批量设置多个键值对（使用单个连接）
    pub async fn piliang_shezhi(&self, jianzhidui: &[(&str, &str)]) -> anyhow::Result<()> {
        if jianzhidui.is_empty() {
            return Ok(());
        }

        let mut conn = self.huoqu_lianjie().await?;

        for (jian, zhi) in jianzhidui {
            if let Err(e) = conn.set::<&str, &str, ()>(jian, zhi).await {
                shujukuxitong_rizhi("redis", &format!("批量设置键 {} 失败", jian));
                return Err(e.into());
            }
        }

        Ok(())
    }

    /// 批量获取多个键的值（使用单个连接）
    pub async fn piliang_huoqu(&self, jianshu: &[&str]) -> anyhow::Result<Vec<Option<String>>> {
        if jianshu.is_empty() {
            return Ok(Vec::new());
        }

        let mut conn = self.huoqu_lianjie().await?;
        let mut jieguo = Vec::with_capacity(jianshu.len());

        for jian in jianshu {
            match conn.get(*jian).await {
                Ok(zhi) => jieguo.push(zhi),
                Err(e) => {
                    shujukuxitong_rizhi("redis", &format!("批量获取键 {} 失败", jian));
                    return Err(e.into());
                }
            }
        }

        Ok(jieguo)
    }

    /// 在单个连接上执行多个操作（连接作用域管理）
    pub async fn zhixing_with_lianjie<F, R>(&self, caozuo: F) -> anyhow::Result<R>
    where
        F: FnOnce(&mut Connection) -> std::pin::Pin<Box<dyn std::future::Future<Output = anyhow::Result<R>> + Send + '_>>,
        R: Send,
    {
        let mut conn = self.huoqu_lianjie().await?;
        caozuo(&mut conn).await
    }

    /// 按模式删除Redis键
    pub async fn shanchu_by_pattern(&self, pattern: &str) -> anyhow::Result<u64> {
        if self.lianjiechi.is_none() {
            return Err(anyhow::anyhow!("Redis连接池未初始化"));
        }

        let mut conn = self.huoqu_lianjie().await?;

        // 使用SCAN命令获取匹配的键
        let keys: Vec<String> = conn.keys(pattern).await?;

        if keys.is_empty() {
            return Ok(0);
        }

        // 批量删除键
        let shanchu_shuliang: u64 = conn.del(&keys).await?;

        shujukuxitong_rizhi("redis", &format!("按模式 '{}' 删除了 {} 个键", pattern, shanchu_shuliang));
        Ok(shanchu_shuliang)
    }

    /// 统计匹配模式的键数量
    pub async fn count_keys_by_pattern(&self, pattern: &str) -> anyhow::Result<u64> {
        if self.lianjiechi.is_none() {
            return Err(anyhow::anyhow!("Redis连接池未初始化"));
        }

        let mut conn = self.huoqu_lianjie().await?;

        // 使用SCAN命令获取匹配的键
        let keys: Vec<String> = conn.keys(pattern).await?;

        Ok(keys.len() as u64)
    }


}