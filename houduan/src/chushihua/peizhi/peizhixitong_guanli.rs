#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::sync::{Arc, RwLock};
use std::fs;
use anyhow::Result;
use crate::rizhixitong::{rizhixitong_xinxi_with_moshi, rizhixitong_jinggao_with_moshi, rizhixitong_cuowu_with_moshi, rizhixitong_shuchu_moshi};
use super::zongpeizhi_peizhi::peizhixitong_zongpeizhi;

/// 配置系统管理器
/// 
/// 负责配置的创建、加载、热更新等所有管理逻辑
/// 通过调用配置结构体来实现具体功能，保证扩展性
pub struct peizhixitong_guanli {
    peizhi: Arc<RwLock<peizhixitong_zongpeizhi>>,
}

impl peizhixitong_guanli {
    /// 创建新的配置管理器
    pub fn peizhixitong_new() -> Self {
        Self {
            peizhi: Arc::new(RwLock::new(peizhixitong_zongpeizhi::default())),
        }
    }
    
    /// 初始化配置系统
    ///
    /// 尝试读取配置文件，如果失败则创建默认配置文件
    pub fn peizhixitong_chushihua(&self) -> Result<()> {
        // 尝试从文件读取配置
        match self.peizhixitong_cong_wenjian_duqu() {
            Ok(peizhi) => {
                // 成功读取，更新内存中的配置
                match self.peizhi.write() {
                    Ok(mut guard) => {
                        *guard = peizhi;
                        rizhixitong_xinxi_with_moshi(
                            "配置系统初始化成功啦！",
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("更新内存配置失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!("更新内存配置失败: {}", e))
                    }
                }
            }
            Err(_) => {
                // 读取失败，创建默认配置文件
                self.peizhixitong_chuangjian_moren_wenjian()?;

                rizhixitong_xinxi_with_moshi(
                    "配置系统初始化成功啦！",
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
        }
    }
    
    /// 从文件读取配置
    fn peizhixitong_cong_wenjian_duqu(&self) -> Result<peizhixitong_zongpeizhi> {
        let wenjian_lujing = peizhixitong_zongpeizhi::peizhixitong_get_wenjian_lujing();

        match fs::read_to_string(&wenjian_lujing) {
            Ok(neirong) => {
                match serde_yaml::from_str::<peizhixitong_zongpeizhi>(&neirong) {
                    Ok(peizhi) => {
                        Ok(peizhi)
                    }
                    Err(e) => {
                        Err(anyhow::anyhow!("配置文件解析失败: {}", e))
                    }
                }
            }
            Err(e) => {
                Err(anyhow::anyhow!("读取配置文件失败: {}", e))
            }
        }
    }
    
    /// 创建默认配置文件
    fn peizhixitong_chuangjian_moren_wenjian(&self) -> Result<()> {
        let wenjian_lujing = peizhixitong_zongpeizhi::peizhixitong_get_wenjian_lujing();
        let moren_peizhi = peizhixitong_zongpeizhi::default();
        
        rizhixitong_xinxi_with_moshi(
            &format!("创建默认配置文件: {}", wenjian_lujing),
            rizhixitong_shuchu_moshi::xiangxi
        );
        
        self.peizhixitong_baocun_peizhi_dao_wenjian(&moren_peizhi)
    }
    
    /// 保存配置到文件
    fn peizhixitong_baocun_peizhi_dao_wenjian(&self, peizhi: &peizhixitong_zongpeizhi) -> Result<()> {
        let wenjian_lujing = peizhixitong_zongpeizhi::peizhixitong_get_wenjian_lujing();
        
        match serde_yaml::to_string(peizhi) {
            Ok(yaml_neirong) => {
                match fs::write(&wenjian_lujing, yaml_neirong) {
                    Ok(_) => {
                        rizhixitong_xinxi_with_moshi(
                            "配置文件保存成功",
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("写入配置文件失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!("写入配置文件失败: {}", e))
                    }
                }
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("序列化配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!("序列化配置失败: {}", e))
            }
        }
    }
    
    /// 热加载配置文件
    pub fn peizhixitong_re_jiazai(&self) -> Result<()> {
        rizhixitong_xinxi_with_moshi(
            "开始热加载配置文件...",
            rizhixitong_shuchu_moshi::xiangxi
        );
        
        match self.peizhixitong_cong_wenjian_duqu() {
            Ok(xin_peizhi) => {
                match self.peizhi.write() {
                    Ok(mut guard) => {
                        *guard = xin_peizhi;
                        rizhixitong_xinxi_with_moshi(
                            "配置热加载成功",
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("更新内存配置失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!("更新内存配置失败: {}", e))
                    }
                }
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("热加载配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Err(e)
            }
        }
    }
    
    /// 获取当前配置的克隆
    pub fn peizhixitong_get_peizhi(&self) -> Result<peizhixitong_zongpeizhi> {
        match self.peizhi.read() {
            Ok(guard) => Ok(guard.clone()),
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("读取配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!("读取配置失败: {}", e))
            }
        }
    }
    
    /// 更新配置并保存到文件
    pub fn peizhixitong_gengxin_peizhi(&self, xin_peizhi: peizhixitong_zongpeizhi) -> Result<()> {
        rizhixitong_xinxi_with_moshi(
            "更新配置...",
            rizhixitong_shuchu_moshi::xiangxi
        );
        
        // 先保存到文件
        self.peizhixitong_baocun_peizhi_dao_wenjian(&xin_peizhi)?;
        
        // 再更新内存
        match self.peizhi.write() {
            Ok(mut guard) => {
                *guard = xin_peizhi;
                rizhixitong_xinxi_with_moshi(
                    "配置更新成功",
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("更新内存配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!("更新内存配置失败: {}", e))
            }
        }
    }
}
