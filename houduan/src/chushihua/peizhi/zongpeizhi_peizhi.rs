#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};

/// 数据库配置结构体
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct peizhixitong_shujuku_peizhi {
    pub shujukuip: String,    // 数据库IP
    pub duankou: u16,         // 端口
    pub yonghu: String,       // 用户名
    pub mima: String,         // 密码
}

impl Default for peizhixitong_shujuku_peizhi {
    fn default() -> Self {
        Self {
            shujukuip: "127.0.0.1".to_string(),
            duankou: 3306,
            yonghu: "root".to_string(),
            mima: "111222".to_string(),
        }
    }
}

/// Redis配置结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct peizhixitong_redis_peizhi {
    pub ip: String,
    pub duankou: u16,
    pub yonghu: String,
    pub mima: String,
}

impl Default for peizhixitong_redis_peizhi {
    fn default() -> Self {
        Self {
            ip: "127.0.0.1".to_string(),
            duankou: 6379,
            yonghu: "root".to_string(),
            mima: "111222".to_string(),
        }
    }
}

/// 总配置结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct peizhixitong_zongpeizhi {
    pub yunxingduankou: u16,                              // 运行端口
    pub shujuku: peizhixitong_shujuku_peizhi,            // 数据库配置
    pub redis: peizhixitong_redis_peizhi,                // Redis配置
}

impl Default for peizhixitong_zongpeizhi {
    fn default() -> Self {
        Self {
            yunxingduankou: 8098,
            shujuku: peizhixitong_shujuku_peizhi::default(),
            redis: peizhixitong_redis_peizhi::default(),
        }
    }
}

impl peizhixitong_zongpeizhi {
    /// 获取配置文件名
    pub fn peizhixitong_get_wenjianming() -> &'static str {
        "zongpeizhi.yml"
    }

    /// 获取配置文件完整路径
    pub fn peizhixitong_get_wenjian_lujing() -> String {
        format!("./peizhi/{}", Self::peizhixitong_get_wenjianming())
    }
}


