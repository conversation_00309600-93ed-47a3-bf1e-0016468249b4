#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::houduan_ecdh_miyaojiaohuan::*;
use super::qianduan_ecdh_miyaojiaohuan::*;
use super::miya<PERSON>jiaohuan_zhongjianjian::*;
use serde_json;

/// 直观展示前后端密钥交换的完整流程
///
/// 这个函数演示了完整的ECDH密钥交换流程：
/// 1. 前后端创建密钥交换器
/// 2. 前端生成密钥交换请求
/// 3. 后端处理请求并计算共享密钥
/// 4. 前端处理响应并计算共享密钥
/// 5. 验证双方共享密钥一致性
/// 6. 显示最终的共享密钥
pub fn zhiguan_ceshi_miyao_jiaohuan_liucheng() {
    println!("\n🔐 ===== ECDH密钥交换完整流程演示 =====\n");

    // 第一步：创建前端和后端密钥交换器
    println!("📱 第1步：创建前端和后端密钥交换器");
    let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();
    let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
    
    println!("   ✅ 前端密钥交换器已创建，客户端ID: {}", qianduan_ecdh.get_kehuduan_id());
    println!("   ✅ 后端密钥交换器已创建，会话ID: {}", houduan_ecdh.get_huihua_id());

    // 第二步：前端生成密钥交换请求
    println!("\n📤 第2步：前端生成密钥交换请求");
    let qianduan_qingqiu = qianduan_ecdh.chuangjian_miyao_qingqiu().unwrap();
    
    println!("   📋 前端发送的请求数据：");
    println!("      - 客户端ID: {}", qianduan_qingqiu.kehuduan_gongyao.kehuduan_id);
    println!("      - 客户端公钥: {}...", &qianduan_qingqiu.kehuduan_gongyao.gongyao[..20]);
    println!("      - 时间戳: {}", qianduan_qingqiu.kehuduan_gongyao.shijian_chuo);
    println!("      - 浏览器信息: {:?}", qianduan_qingqiu.kehuduan_gongyao.liulanqi_xinxi);
    println!("      - 请求类型: {}", qianduan_qingqiu.qingqiu_leixing);

    // 第三步：后端处理请求并生成响应
    println!("\n🖥️  第3步：后端处理密钥交换请求");
    
    // 后端验证客户端公钥
    let gongyao_youxiao = houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi(
        &qianduan_qingqiu.kehuduan_gongyao.gongyao
    ).unwrap();
    println!("   🔍 后端验证客户端公钥: {}", if gongyao_youxiao { "✅ 有效" } else { "❌ 无效" });

    // 后端计算共享密钥
    let houduan_gongxiang_miyao = houduan_ecdh.jisuan_gongxiang_miyao_with_kehuduan_id(
        &qianduan_qingqiu.kehuduan_gongyao.gongyao,
        Some(&qianduan_qingqiu.kehuduan_gongyao.kehuduan_id)
    ).unwrap();
    
    println!("   🔑 后端计算出的共享密钥信息：");
    println!("      - 原始共享密钥长度: {} 字节", houduan_gongxiang_miyao.yuanshi_miyao.len());
    println!("      - AES密钥长度: {} 字节", houduan_gongxiang_miyao.aes_miyao.len());
    println!("      - AES密钥前8字节: {:02x?}", &houduan_gongxiang_miyao.aes_miyao[..8]);

    // 后端生成响应
    let houduan_gongyao_shuju = houduan_ecdh.get_gongyao_shuju().unwrap();
    let houduan_xiangying = qianduan_miyao_xiangying {
        fuwuqi_gongyao: houduan_gongyao_shuju.gongyao,
        fuwuqi_huihua_id: houduan_gongyao_shuju.huihua_id,
        zhuangtai: "success".to_string(),
        cuowu_xinxi: None,
    };

    println!("   📋 后端返回的响应数据：");
    println!("      - 服务器公钥: {}...", &houduan_xiangying.fuwuqi_gongyao[..20]);
    println!("      - 服务器会话ID: {}", houduan_xiangying.fuwuqi_huihua_id);
    println!("      - 响应状态: {}", houduan_xiangying.zhuangtai);

    // 第四步：前端处理响应并计算共享密钥
    println!("\n📱 第4步：前端处理服务器响应");
    
    // 前端验证服务器响应
    let xiangying_youxiao = qianduan_ecdh_miyaojiaohuan::yanzheng_fuwuqi_xiangying(&houduan_xiangying).unwrap();
    println!("   🔍 前端验证服务器响应: {}", if xiangying_youxiao { "✅ 有效" } else { "❌ 无效" });

    // 前端计算共享密钥
    let qianduan_gongxiang_miyao = qianduan_ecdh.chuli_fuwuqi_xiangying(&houduan_xiangying).unwrap();

    println!("   🔑 前端计算出的共享密钥信息：");
    println!("      - 原始共享密钥长度: {} 字节", qianduan_gongxiang_miyao.yuanshi_miyao.len());
    println!("      - AES密钥长度: {} 字节", qianduan_gongxiang_miyao.aes_miyao.len());
    println!("      - AES密钥前8字节: {:02x?}", &qianduan_gongxiang_miyao.aes_miyao[..8]);

    // 验证密钥是否相同
    let miyao_yizhi = houduan_gongxiang_miyao.aes_miyao == qianduan_gongxiang_miyao.aes_miyao;
    println!("   🔐 密钥一致性检查: {}", if miyao_yizhi { "✅ 一致" } else { "❌ 不一致" });

    // 第五步：显示前端和后端的密钥信息
    println!("\n� 第5步：显示前端和后端的密钥信息");

    // 获取后端公钥和私钥信息
    let houduan_gongyao_shuju = houduan_ecdh.get_gongyao_shuju().unwrap();
    println!("   📋 后端密钥信息：");
    println!("      - 后端公钥: {}", houduan_gongyao_shuju.gongyao);
    println!("      - 后端会话ID: {}", houduan_gongyao_shuju.huihua_id);
    println!("      - 后端共享密钥: {:02x?}", houduan_gongxiang_miyao.aes_miyao);

    // 获取前端公钥信息
    let qianduan_gongyao_shuju = qianduan_ecdh.get_gongyao_shuju().unwrap();
    println!("   📋 前端密钥信息：");
    println!("      - 前端公钥: {}", qianduan_gongyao_shuju.gongyao);
    println!("      - 前端客户端ID: {}", qianduan_gongyao_shuju.kehuduan_id);
    println!("      - 前端共享密钥: {:02x?}", qianduan_gongxiang_miyao.aes_miyao);

    // 总结
    println!("\n🎉 ===== 密钥交换流程演示完成 =====");
    println!("✅ 密钥交换成功");
    println!("✅ 双方共享密钥一致");
    println!("🔐 密钥传输完成，可用于后续安全通信！\n");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_zhiguan_miyao_jiaohuan_liucheng() {
        zhiguan_ceshi_miyao_jiaohuan_liucheng();
    }

    #[test]
    fn test_json_xulie_hua() {
        println!("\n📋 ===== JSON序列化测试 =====\n");

        // 创建前端密钥交换器
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();
        let qingqiu = qianduan_ecdh.chuangjian_miyao_qingqiu().unwrap();

        // 序列化请求
        let qingqiu_json = serde_json::to_string_pretty(&qingqiu).unwrap();
        println!("📤 前端发送的JSON请求：");
        println!("{}", qingqiu_json);

        // 创建后端响应
        let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let gongyao_shuju = houduan_ecdh.get_gongyao_shuju().unwrap();
        let xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: gongyao_shuju.gongyao,
            fuwuqi_huihua_id: gongyao_shuju.huihua_id,
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };

        // 序列化响应
        let xiangying_json = serde_json::to_string_pretty(&xiangying).unwrap();
        println!("\n📥 后端返回的JSON响应：");
        println!("{}", xiangying_json);

        // 验证密钥交换
        let gongxiang_miyao = qianduan_ecdh.chuli_fuwuqi_xiangying(&xiangying).unwrap();
        println!("\n🔑 密钥交换成功，共享密钥长度: {} 字节", gongxiang_miyao.aes_miyao.len());

        println!("\n✅ JSON序列化测试完成\n");
    }

    #[test]
    fn test_chongxian_gongneng() {
        println!("\n🔄 ===== 重现功能测试 =====\n");

        // 创建后端密钥交换器
        let mut houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let _huihua_id = houduan_ecdh.get_huihua_id().to_string();

        // 获取公钥数据并存储
        let gongyao_shuju = houduan_ecdh.get_gongyao_shuju().unwrap();
        houduan_ecdh.cunchu_gongyao_shuju(gongyao_shuju.clone());

        println!("📋 原始公钥数据：");
        println!("   - 会话ID: {}", gongyao_shuju.huihua_id);
        println!("   - 公钥: {}...", &gongyao_shuju.gongyao[..20]);
        println!("   - 时间戳: {}", gongyao_shuju.shijian_chuo);

        // 测试重现功能
        let chongxian_gongyao = houduan_ecdh.chongxian_gongyao_shuju().unwrap();
        println!("\n🔄 重现的公钥数据：");
        println!("   - 会话ID: {}", chongxian_gongyao.huihua_id);
        println!("   - 公钥: {}...", &chongxian_gongyao.gongyao[..20]);
        println!("   - 时间戳: {}", chongxian_gongyao.shijian_chuo);

        // 验证重现数据一致性
        assert_eq!(gongyao_shuju.huihua_id, chongxian_gongyao.huihua_id);
        assert_eq!(gongyao_shuju.gongyao, chongxian_gongyao.gongyao);
        assert_eq!(gongyao_shuju.shijian_chuo, chongxian_gongyao.shijian_chuo);

        println!("\n✅ 重现功能测试通过\n");
    }

    #[test]
    fn test_jiaohuan_gongneng() {
        println!("\n🔄 ===== 交换功能测试 =====\n");

        // 创建两个密钥交换器
        let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();

        // 获取双方公钥数据
        let houduan_gongyao = houduan_ecdh.get_gongyao_shuju().unwrap();
        let qianduan_gongyao = qianduan_ecdh.get_gongyao_shuju().unwrap();

        println!("📋 后端公钥: {}...", &houduan_gongyao.gongyao[..20]);
        println!("📋 前端公钥: {}...", &qianduan_gongyao.gongyao[..20]);

        // 双方计算共享密钥（需要使用带客户端ID的方法保证一致性）
        let houduan_gongxiang = houduan_ecdh.jisuan_gongxiang_miyao_with_kehuduan_id(
            &qianduan_gongyao.gongyao,
            Some(qianduan_ecdh.get_kehuduan_id())
        ).unwrap();
        let qianduan_xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: houduan_gongyao.gongyao.clone(),
            fuwuqi_huihua_id: houduan_gongyao.huihua_id.clone(),
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };
        let qianduan_gongxiang = qianduan_ecdh.chuli_fuwuqi_xiangying(&qianduan_xiangying).unwrap();

        // 验证共享密钥一致性
        assert_eq!(houduan_gongxiang.aes_miyao, qianduan_gongxiang.aes_miyao);

        println!("🔑 共享密钥: {:02x?}", houduan_gongxiang.aes_miyao);
        println!("\n✅ 交换功能测试通过\n");
    }

    #[test]
    fn test_chongxian_jiaohuan() {
        println!("\n🔄 ===== 重现交换功能测试 =====\n");

        // 创建密钥交换管理器
        let guanliqii = miyaojiaohuan_guanliqii::new();

        // 创建前端密钥交换器并生成请求
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();
        let qingqiu = qianduan_ecdh.chuangjian_miyao_qingqiu().unwrap();

        // 转换为中间件请求格式
        let zhongjianjian_qingqiu = miyaojiaohuan_qingqiu {
            kehuduan_gongyao: qingqiu.kehuduan_gongyao.gongyao.clone(),
            kehuduan_id: qingqiu.kehuduan_gongyao.kehuduan_id.clone(),
            shijian_chuo: Some(qingqiu.kehuduan_gongyao.shijian_chuo),
        };

        // 处理密钥交换请求
        let xiangying = guanliqii.chuli_miyao_jiaohuan(&zhongjianjian_qingqiu).unwrap();
        let huihua_id = xiangying.huihua_id.clone();

        println!("📋 初始交换完成，会话ID: {}", huihua_id);

        // 测试重现公钥数据
        let chongxian_gongyao = guanliqii.chongxian_gongyao_shuju(&huihua_id).unwrap();
        println!("🔄 重现公钥: {}...", &chongxian_gongyao.gongyao[..20]);

        // 测试重现交换功能
        let chongxian_gongxiang = guanliqii.chongxian_jiaohuan_miyao(&huihua_id, &qingqiu.kehuduan_gongyao.gongyao).unwrap();
        println!("🔑 重现共享密钥: {:02x?}", chongxian_gongxiang.aes_miyao);

        // 验证重现的共享密钥与原始计算结果一致
        let yuanshi_gongxiang = guanliqii.get_gongxiang_miyao(&huihua_id).unwrap().unwrap();
        assert_eq!(chongxian_gongxiang.aes_miyao, yuanshi_gongxiang.aes_miyao);

        println!("\n✅ 重现交换功能测试通过\n");
    }
}
