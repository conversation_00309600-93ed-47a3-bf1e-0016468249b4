#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use aes_gcm::{
    aead::{Aead, AeadCore, KeyInit, OsRng},
    Aes256Gcm, Nonce, Key
};
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose};
// SHA256已在ECDH密钥交换中使用，这里不需要额外导入

use super::houduan_ecdh_miyaojiaohuan::houduan_gongxiang_miyao;
use super::qianduan_ecdh_miyaojiaohuan::qianduan_gongxiang_miyao;

/// 加密消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jiami_xiaoxi {
    /// 加密后的数据（Base64编码）
    pub jiami_shuju: String,
    /// 随机数（Base64编码）
    pub suijishu: String,
    /// 会话ID
    pub huihua_id: String,
    /// 消息ID
    pub xiaoxi_id: String,
    /// 时间戳
    pub shijian_chuo: u64,
}

/// 解密消息结构
#[derive(Debug, Clone)]
pub struct jiemi_xiaoxi {
    /// 原始数据
    pub yuanshi_shuju: Vec<u8>,
    /// 会话ID
    pub huihua_id: String,
    /// 消息ID
    pub xiaoxi_id: String,
    /// 时间戳
    pub shijian_chuo: u64,
}

/// 后端加密传输器
pub struct houduan_jiamichuanshu {
    /// 共享密钥
    gongxiang_miyao: houduan_gongxiang_miyao,
    /// AES-GCM加密器
    cipher: Aes256Gcm,
}

/// 前端加密传输器
pub struct qianduan_jiamichuanshu {
    /// 共享密钥
    gongxiang_miyao: qianduan_gongxiang_miyao,
    /// AES-GCM加密器
    cipher: Aes256Gcm,
}

impl houduan_jiamichuanshu {
    /// 创建后端加密传输器
    pub fn new(gongxiang_miyao: houduan_gongxiang_miyao) -> Result<Self> {
        // 确保AES密钥长度为32字节
        if gongxiang_miyao.aes_miyao.len() != 32 {
            return Err(anyhow!("AES密钥长度必须为32字节"));
        }

        let key = Key::<Aes256Gcm>::from_slice(&gongxiang_miyao.aes_miyao);
        let cipher = Aes256Gcm::new(key);

        Ok(Self {
            gongxiang_miyao,
            cipher,
        })
    }

    /// 加密数据
    pub fn jiami(&self, shuju: &[u8]) -> Result<jiami_xiaoxi> {
        // 生成随机nonce
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // 加密数据
        let jiami_shuju = self.cipher.encrypt(&nonce, shuju)
            .map_err(|e| anyhow!("加密失败: {}", e))?;

        // 编码为Base64
        let jiami_shuju_base64 = general_purpose::STANDARD.encode(&jiami_shuju);
        let suijishu_base64 = general_purpose::STANDARD.encode(&nonce);

        Ok(jiami_xiaoxi {
            jiami_shuju: jiami_shuju_base64,
            suijishu: suijishu_base64,
            huihua_id: self.gongxiang_miyao.huihua_id.clone(),
            xiaoxi_id: uuid::Uuid::new_v4().to_string(),
            shijian_chuo: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }

    /// 解密数据
    pub fn jiemi(&self, jiami_xiaoxi: &jiami_xiaoxi) -> Result<jiemi_xiaoxi> {
        // 验证会话ID
        if jiami_xiaoxi.huihua_id != self.gongxiang_miyao.huihua_id {
            return Err(anyhow!("会话ID不匹配"));
        }

        // 解码Base64
        let jiami_shuju = general_purpose::STANDARD.decode(&jiami_xiaoxi.jiami_shuju)
            .map_err(|e| anyhow!("解码加密数据失败: {}", e))?;
        
        let nonce_bytes = general_purpose::STANDARD.decode(&jiami_xiaoxi.suijishu)
            .map_err(|e| anyhow!("解码随机数失败: {}", e))?;

        // 创建nonce
        if nonce_bytes.len() != 12 {
            return Err(anyhow!("随机数长度必须为12字节"));
        }
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 解密数据
        let yuanshi_shuju = self.cipher.decrypt(nonce, jiami_shuju.as_ref())
            .map_err(|e| anyhow!("解密失败: {}", e))?;

        Ok(jiemi_xiaoxi {
            yuanshi_shuju,
            huihua_id: jiami_xiaoxi.huihua_id.clone(),
            xiaoxi_id: jiami_xiaoxi.xiaoxi_id.clone(),
            shijian_chuo: jiami_xiaoxi.shijian_chuo,
        })
    }

    /// 加密字符串
    pub fn jiami_zifuchuan(&self, wenben: &str) -> Result<jiami_xiaoxi> {
        self.jiami(wenben.as_bytes())
    }

    /// 解密为字符串
    pub fn jiemi_wei_zifuchuan(&self, jiami_xiaoxi: &jiami_xiaoxi) -> Result<String> {
        let jiemi_jieguo = self.jiemi(jiami_xiaoxi)?;
        String::from_utf8(jiemi_jieguo.yuanshi_shuju)
            .map_err(|e| anyhow!("转换为UTF-8字符串失败: {}", e))
    }

    /// 获取会话ID
    pub fn get_huihua_id(&self) -> &str {
        &self.gongxiang_miyao.huihua_id
    }
}

impl qianduan_jiamichuanshu {
    /// 创建前端加密传输器
    pub fn new(gongxiang_miyao: qianduan_gongxiang_miyao) -> Result<Self> {
        // 确保AES密钥长度为32字节
        if gongxiang_miyao.aes_miyao.len() != 32 {
            return Err(anyhow!("AES密钥长度必须为32字节"));
        }

        let key = Key::<Aes256Gcm>::from_slice(&gongxiang_miyao.aes_miyao);
        let cipher = Aes256Gcm::new(key);

        Ok(Self {
            gongxiang_miyao,
            cipher,
        })
    }

    /// 加密数据
    pub fn jiami(&self, shuju: &[u8]) -> Result<jiami_xiaoxi> {
        // 生成随机nonce
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // 加密数据
        let jiami_shuju = self.cipher.encrypt(&nonce, shuju)
            .map_err(|e| anyhow!("加密失败: {}", e))?;

        // 编码为Base64
        let jiami_shuju_base64 = general_purpose::STANDARD.encode(&jiami_shuju);
        let suijishu_base64 = general_purpose::STANDARD.encode(&nonce);

        // 使用服务器会话ID（如果有）
        let huihua_id = self.gongxiang_miyao.fuwuqi_huihua_id
            .as_ref()
            .unwrap_or(&self.gongxiang_miyao.kehuduan_id)
            .clone();

        Ok(jiami_xiaoxi {
            jiami_shuju: jiami_shuju_base64,
            suijishu: suijishu_base64,
            huihua_id,
            xiaoxi_id: uuid::Uuid::new_v4().to_string(),
            shijian_chuo: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }

    /// 解密数据
    pub fn jiemi(&self, jiami_xiaoxi: &jiami_xiaoxi) -> Result<jiemi_xiaoxi> {
        // 验证会话ID
        let qiwang_huihua_id = self.gongxiang_miyao.fuwuqi_huihua_id
            .as_ref()
            .unwrap_or(&self.gongxiang_miyao.kehuduan_id);
        
        if jiami_xiaoxi.huihua_id != *qiwang_huihua_id {
            return Err(anyhow!("会话ID不匹配"));
        }

        // 解码Base64
        let jiami_shuju = general_purpose::STANDARD.decode(&jiami_xiaoxi.jiami_shuju)
            .map_err(|e| anyhow!("解码加密数据失败: {}", e))?;
        
        let nonce_bytes = general_purpose::STANDARD.decode(&jiami_xiaoxi.suijishu)
            .map_err(|e| anyhow!("解码随机数失败: {}", e))?;

        // 创建nonce
        if nonce_bytes.len() != 12 {
            return Err(anyhow!("随机数长度必须为12字节"));
        }
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 解密数据
        let yuanshi_shuju = self.cipher.decrypt(nonce, jiami_shuju.as_ref())
            .map_err(|e| anyhow!("解密失败: {}", e))?;

        Ok(jiemi_xiaoxi {
            yuanshi_shuju,
            huihua_id: jiami_xiaoxi.huihua_id.clone(),
            xiaoxi_id: jiami_xiaoxi.xiaoxi_id.clone(),
            shijian_chuo: jiami_xiaoxi.shijian_chuo,
        })
    }

    /// 加密字符串
    pub fn jiami_zifuchuan(&self, wenben: &str) -> Result<jiami_xiaoxi> {
        self.jiami(wenben.as_bytes())
    }

    /// 解密为字符串
    pub fn jiemi_wei_zifuchuan(&self, jiami_xiaoxi: &jiami_xiaoxi) -> Result<String> {
        let jiemi_jieguo = self.jiemi(jiami_xiaoxi)?;
        String::from_utf8(jiemi_jieguo.yuanshi_shuju)
            .map_err(|e| anyhow!("转换为UTF-8字符串失败: {}", e))
    }

    /// 获取客户端ID
    pub fn get_kehuduan_id(&self) -> &str {
        &self.gongxiang_miyao.kehuduan_id
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::houduan_ecdh_miyaojiaohuan::*;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::qianduan_ecdh_miyaojiaohuan::*;

    #[test]
    fn test_jiamichuanshu_gongneng() {
        // 创建ECDH密钥交换
        let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();

        // 交换公钥
        let houduan_gongyao = houduan_ecdh.get_gongyao_shuju().unwrap();
        let qianduan_gongyao = qianduan_ecdh.get_gongyao_shuju().unwrap();

        // 计算共享密钥
        let houduan_gongxiang = houduan_ecdh.jisuan_gongxiang_miyao_with_kehuduan_id(
            &qianduan_gongyao.gongyao,
            Some(&qianduan_gongyao.kehuduan_id)
        ).unwrap();

        let xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: houduan_gongyao.gongyao.clone(),
            fuwuqi_huihua_id: houduan_gongyao.huihua_id.clone(),
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };
        let qianduan_gongxiang = qianduan_ecdh.chuli_fuwuqi_xiangying(&xiangying).unwrap();

        // 创建加密传输器
        let houduan_jiami = houduan_jiamichuanshu::new(houduan_gongxiang).unwrap();
        let qianduan_jiami = qianduan_jiamichuanshu::new(qianduan_gongxiang).unwrap();

        // 测试加密解密
        let yuanshi_wenben = "这是一个测试消息";

        // 验证共享密钥相同
        assert_eq!(houduan_jiami.gongxiang_miyao.aes_miyao, qianduan_jiami.gongxiang_miyao.aes_miyao);

        // 简单测试：后端加密后端解密
        let houduan_jiami_xiaoxi = houduan_jiami.jiami_zifuchuan(yuanshi_wenben).unwrap();
        let houduan_jiemi_wenben = houduan_jiami.jiemi_wei_zifuchuan(&houduan_jiami_xiaoxi).unwrap();
        assert_eq!(yuanshi_wenben, houduan_jiemi_wenben);

        // 简单测试：前端加密前端解密
        let qianduan_jiami_xiaoxi = qianduan_jiami.jiami_zifuchuan(yuanshi_wenben).unwrap();
        let qianduan_jiemi_wenben = qianduan_jiami.jiemi_wei_zifuchuan(&qianduan_jiami_xiaoxi).unwrap();
        assert_eq!(yuanshi_wenben, qianduan_jiemi_wenben);
    }
}
