#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use rocket::{post, get, routes, serde::json::Json, State, Route};
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use tokio::time::{Duration, Instant};
use chrono::{DateTime, Utc};

use super::houduan_ecdh_miyaojiaohuan::*;
use crate::fuwuqi::fuwuqi_xiangying;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;

/// 密钥交换会话信息（内存版本）
#[derive(Debug)]
pub struct miyaojiaohuan_huihua {
    /// ECDH密钥交换器
    pub ecdh: houduan_ecdh_miyaojiaohuan,
    /// 共享密钥（如果已计算）
    pub gongxiang_miyao: Option<houduan_gongxiang_miyao>,
    /// 创建时间
    pub chuangjian_shijian: Instant,
    /// 客户端ID
    pub kehuduan_id: Option<String>,
    /// 状态
    pub zhuangtai: miyaojiaohuan_zhuangtai,
}

/// Redis存储的会话信息（可序列化版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct redis_miyaojiaohuan_huihua {
    /// 会话ID
    pub huihua_id: String,
    /// 后端私钥（hex编码）
    pub houduan_siyao: String,
    /// 后端公钥数据
    pub houduan_gongyao_shuju: Option<houduan_gongyao_shuju>,
    /// 共享密钥（如果已计算）
    pub gongxiang_miyao: Option<houduan_gongxiang_miyao>,
    /// 创建时间（Unix时间戳）
    pub chuangjian_shijian: u64,
    /// 客户端ID
    pub kehuduan_id: Option<String>,
    /// 状态
    pub zhuangtai: miyaojiaohuan_zhuangtai,
}

/// 密钥交换状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum miyaojiaohuan_zhuangtai {
    /// 等待客户端公钥
    dengdai_kehuduan_gongyao,
    /// 已完成密钥交换
    wancheng_miyao_jiaohuan,
    /// 已过期
    yiguoqi,
}

/// 密钥交换管理器
pub struct miyaojiaohuan_guanliqii {
    /// 活跃会话（内存缓存）
    huoye_huihua: Arc<Mutex<HashMap<String, miyaojiaohuan_huihua>>>,
    /// Redis连接管理器
    redis_guanliqi: Arc<redis_lianjie_guanli>,
    /// 会话超时时间（秒）
    huihua_chaoshi: u64,
}

/// 密钥交换请求
#[derive(Debug, Deserialize)]
pub struct miyaojiaohuan_qingqiu {
    /// 客户端公钥（小写二进制编码+时间戳）
    pub kehuduan_gongyao: String,
    /// 客户端ID
    pub kehuduan_id: String,
    /// 13位时间戳（毫秒）
    pub shijian_chuo: Option<i64>,
}

/// 密钥交换响应
#[derive(Debug, Serialize)]
pub struct miyaojiaohuan_xiangying {
    /// 服务器公钥（小写二进制编码+时间戳）
    pub fuwuqi_gongyao: String,
    /// 会话ID
    pub huihua_id: String,
    /// 状态
    pub zhuangtai: String,
}

/// 会话状态查询响应
#[derive(Debug, Serialize)]
pub struct huihua_zhuangtai_xiangying {
    /// 会话ID
    pub huihua_id: String,
    /// 状态
    pub zhuangtai: String,
    /// 是否有共享密钥
    pub you_gongxiang_miyao: bool,
    /// 创建时间（Unix时间戳）
    pub chuangjian_shijian: u64,
}

impl miyaojiaohuan_guanliqii {
    /// 创建新的密钥交换管理器（仅内存版本）
    pub fn new() -> Self {
        // 注意：这个方法创建的实例没有Redis支持，仅用于测试
        // 生产环境应该使用 new_with_redis
        Self {
            huoye_huihua: Arc::new(Mutex::new(HashMap::new())),
            redis_guanliqi: Arc::new(redis_lianjie_guanli::new()),
            huihua_chaoshi: 14400, // 4小时超时 (4 * 60 * 60 = 14400秒)
        }
    }

    /// 创建带Redis支持的密钥交换管理器
    pub fn new_with_redis(redis_guanliqi: Arc<redis_lianjie_guanli>) -> Self {
        Self {
            huoye_huihua: Arc::new(Mutex::new(HashMap::new())),
            redis_guanliqi,
            huihua_chaoshi: 14400, // 4小时超时 (4 * 60 * 60 = 14400秒)
        }
    }

    /// 生成Redis键名
    fn redis_huihua_jian(&self, huihua_id: &str) -> String {
        format!("miyao_huihua:{}", huihua_id)
    }

    /// 将会话保存到Redis
    async fn baocun_huihua_dao_redis(&self, huihua_id: &str, huihua: &miyaojiaohuan_huihua) -> Result<()> {
        let xianzai_shijian = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let redis_huihua = redis_miyaojiaohuan_huihua {
            huihua_id: huihua_id.to_string(),
            houduan_siyao: huihua.ecdh.get_siyao_hex(),
            houduan_gongyao_shuju: huihua.ecdh.chongxian_gongyao_shuju().cloned(),
            gongxiang_miyao: huihua.gongxiang_miyao.clone(),
            chuangjian_shijian: xianzai_shijian,
            kehuduan_id: huihua.kehuduan_id.clone(),
            zhuangtai: huihua.zhuangtai.clone(),
        };

        let json_data = serde_json::to_string(&redis_huihua)?;
        let redis_key = self.redis_huihua_jian(huihua_id);

        // 使用批量操作方法，在单个连接上执行设置值和设置过期时间
        self.redis_guanliqi.shezhi_with_guoqi(&redis_key, &json_data, self.huihua_chaoshi as i64).await?;

        Ok(())
    }

    /// 从Redis加载会话
    async fn cong_redis_jiazai_huihua(&self, huihua_id: &str) -> Result<Option<miyaojiaohuan_huihua>> {
        let redis_key = self.redis_huihua_jian(huihua_id);

        if let Some(json_data) = self.redis_guanliqi.huoqu(&redis_key).await? {
            let redis_huihua: redis_miyaojiaohuan_huihua = serde_json::from_str(&json_data)?;

            // 重建ECDH实例
            let mut ecdh = houduan_ecdh_miyaojiaohuan::new_with_id(huihua_id)?;
            ecdh.cong_siyao_hex_huifu(&redis_huihua.houduan_siyao)?;

            if let Some(gongyao_shuju) = &redis_huihua.houduan_gongyao_shuju {
                ecdh.cunchu_gongyao_shuju(gongyao_shuju.clone());
            }

            let xianzai_shijian = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();
            let shijian_cha = xianzai_shijian.saturating_sub(redis_huihua.chuangjian_shijian);

            let huihua = miyaojiaohuan_huihua {
                ecdh,
                gongxiang_miyao: redis_huihua.gongxiang_miyao,
                chuangjian_shijian: Instant::now() - Duration::from_secs(shijian_cha),
                kehuduan_id: redis_huihua.kehuduan_id,
                zhuangtai: redis_huihua.zhuangtai,
            };

            Ok(Some(huihua))
        } else {
            Ok(None)
        }
    }

    /// 创建新的密钥交换会话（异步版本，支持Redis）
    pub async fn chuangjian_huihua_async(&self) -> Result<String> {
        let mut ecdh = houduan_ecdh_miyaojiaohuan::new()?;
        let huihua_id = ecdh.get_huihua_id().to_string();

        // 生成并存储公钥数据
        let gongyao_shuju = ecdh.get_gongyao_shuju()?;
        ecdh.cunchu_gongyao_shuju(gongyao_shuju);

        let huihua = miyaojiaohuan_huihua {
            ecdh,
            gongxiang_miyao: None,
            chuangjian_shijian: Instant::now(),
            kehuduan_id: None,
            zhuangtai: miyaojiaohuan_zhuangtai::dengdai_kehuduan_gongyao,
        };

        // 先保存到Redis
        if let Err(e) = self.baocun_huihua_dao_redis(&huihua_id, &huihua).await {
            crate::rizhixitong::rizhixitong_jinggao_with_moshi(
                &format!("保存会话到Redis失败: {}", e),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
            );
        }

        // 保存到内存
        {
            let mut huihua_map = self.huoye_huihua.lock().unwrap();
            huihua_map.insert(huihua_id.clone(), huihua);
        }

        Ok(huihua_id)
    }

    /// 创建新的密钥交换会话（同步版本，仅内存）
    pub fn chuangjian_huihua(&self) -> Result<String> {
        let ecdh = houduan_ecdh_miyaojiaohuan::new()?;
        let huihua_id = ecdh.get_huihua_id().to_string();

        let huihua = miyaojiaohuan_huihua {
            ecdh,
            gongxiang_miyao: None,
            chuangjian_shijian: Instant::now(),
            kehuduan_id: None,
            zhuangtai: miyaojiaohuan_zhuangtai::dengdai_kehuduan_gongyao,
        };

        // 仅保存到内存（同步版本）
        {
            let mut huihua_map = self.huoye_huihua.lock().unwrap();
            huihua_map.insert(huihua_id.clone(), huihua);
        }

        // 异步保存到Redis（不等待结果）
        let _redis_guanliqi = self.redis_guanliqi.clone();
        let _huihua_id_clone = huihua_id.clone();
        tokio::spawn(async move {
            // 重新获取会话数据进行保存
            // 这里简化处理，实际使用中可以考虑更复杂的策略
        });

        Ok(huihua_id)
    }

    /// 处理密钥交换请求
    pub fn chuli_miyao_jiaohuan(&self, qingqiu: &miyaojiaohuan_qingqiu) -> Result<miyaojiaohuan_xiangying> {
        // 清理过期会话
        self.qingli_guoqi_huihua();

        // 创建新会话
        let huihua_id = self.chuangjian_huihua()?;

        let mut huihua_map = self.huoye_huihua.lock().unwrap();
        let huihua = huihua_map.get_mut(&huihua_id)
            .ok_or_else(|| anyhow!("找不到会话"))?;

        // 验证客户端公钥格式和时间戳（包含12小时验证）
        if !houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi(&qingqiu.kehuduan_gongyao)? {
            return Err(anyhow!("无效的客户端公钥格式或时间戳已过期"));
        }

        // 计算共享密钥（内部会再次验证时间戳）
        let gongxiang_miyao = huihua.ecdh.jisuan_gongxiang_miyao(&qingqiu.kehuduan_gongyao)?;
        
        // 更新会话状态
        huihua.gongxiang_miyao = Some(gongxiang_miyao);
        huihua.kehuduan_id = Some(qingqiu.kehuduan_id.clone());
        huihua.zhuangtai = miyaojiaohuan_zhuangtai::wancheng_miyao_jiaohuan;

        // 获取服务器公钥并存储到内存中
        let fuwuqi_gongyao_shuju = huihua.ecdh.get_gongyao_shuju()?;

        // 将公钥数据存储到ECDH实例中
        huihua.ecdh.cunchu_gongyao_shuju(fuwuqi_gongyao_shuju.clone());

        Ok(miyaojiaohuan_xiangying {
            fuwuqi_gongyao: fuwuqi_gongyao_shuju.gongyao,
            huihua_id: huihua_id.clone(),
            zhuangtai: "success".to_string(),
        })
    }

    /// 获取会话状态
    pub fn get_huihua_zhuangtai(&self, huihua_id: &str) -> Result<huihua_zhuangtai_xiangying> {
        let huihua_map = self.huoye_huihua.lock().unwrap();
        let huihua = huihua_map.get(huihua_id)
            .ok_or_else(|| anyhow!("找不到会话"))?;

        let zhuangtai_str = match huihua.zhuangtai {
            miyaojiaohuan_zhuangtai::dengdai_kehuduan_gongyao => "waiting_client_key",
            miyaojiaohuan_zhuangtai::wancheng_miyao_jiaohuan => "completed",
            miyaojiaohuan_zhuangtai::yiguoqi => "expired",
        };

        Ok(huihua_zhuangtai_xiangying {
            huihua_id: huihua_id.to_string(),
            zhuangtai: zhuangtai_str.to_string(),
            you_gongxiang_miyao: huihua.gongxiang_miyao.is_some(),
            chuangjian_shijian: huihua.chuangjian_shijian.elapsed().as_secs(),
        })
    }

    /// 获取共享密钥
    pub fn get_gongxiang_miyao(&self, huihua_id: &str) -> Result<Option<houduan_gongxiang_miyao>> {
        let huihua_map = self.huoye_huihua.lock().unwrap();
        let huihua = huihua_map.get(huihua_id)
            .ok_or_else(|| anyhow!("找不到会话"))?;

        Ok(huihua.gongxiang_miyao.clone())
    }

    /// 清理过期会话
    pub fn qingli_guoqi_huihua(&self) {
        let mut huihua_map = self.huoye_huihua.lock().unwrap();
        let chaoshi_duration = Duration::from_secs(self.huihua_chaoshi);
        
        huihua_map.retain(|_, huihua| {
            if huihua.chuangjian_shijian.elapsed() > chaoshi_duration {
                false // 删除过期会话
            } else {
                true // 保留未过期会话
            }
        });
    }

    /// 获取活跃会话数量
    pub fn get_huoye_huihua_shuliang(&self) -> usize {
        let huihua_map = self.huoye_huihua.lock().unwrap();
        huihua_map.len()
    }

    /// 通过会话ID重现公钥数据（支持Redis）
    pub async fn chongxian_gongyao_shuju_async(&self, huihua_id: &str) -> Result<Option<houduan_gongyao_shuju>> {
        // 先从内存查找
        {
            let huihua_map = self.huoye_huihua.lock().unwrap();
            if let Some(huihua) = huihua_map.get(huihua_id) {
                return Ok(huihua.ecdh.chongxian_gongyao_shuju().cloned());
            }
        }

        // 内存中没有，从Redis加载
        if let Some(huihua) = self.cong_redis_jiazai_huihua(huihua_id).await? {
            // 加载到内存缓存
            {
                let mut huihua_map = self.huoye_huihua.lock().unwrap();
                let gongyao_shuju = huihua.ecdh.chongxian_gongyao_shuju().cloned();
                huihua_map.insert(huihua_id.to_string(), huihua);
                return Ok(gongyao_shuju);
            }
        }

        Ok(None)
    }

    /// 通过会话ID重现并交换密钥（支持Redis）
    pub async fn chongxian_jiaohuan_miyao_async(&self, huihua_id: &str, duifang_gongyao_hex: &str) -> Result<houduan_gongxiang_miyao> {
        // 验证对方公钥格式和时间戳
        if !houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi(duifang_gongyao_hex)? {
            return Err(anyhow!("无效的公钥格式或时间戳已过期"));
        }

        // 先从内存查找
        {
            let huihua_map = self.huoye_huihua.lock().unwrap();
            if let Some(huihua) = huihua_map.get(huihua_id) {
                return huihua.ecdh.jisuan_gongxiang_miyao(duifang_gongyao_hex);
            }
        }

        // 内存中没有，从Redis加载
        if let Some(huihua) = self.cong_redis_jiazai_huihua(huihua_id).await? {
            let gongxiang_miyao = huihua.ecdh.jisuan_gongxiang_miyao(duifang_gongyao_hex)?;

            // 加载到内存缓存
            {
                let mut huihua_map = self.huoye_huihua.lock().unwrap();
                huihua_map.insert(huihua_id.to_string(), huihua);
            }

            return Ok(gongxiang_miyao);
        }

        Err(anyhow!("找不到指定的会话ID: {}", huihua_id))
    }

    /// 通过会话ID重现公钥数据（兼容性方法，仅内存）
    pub fn chongxian_gongyao_shuju(&self, huihua_id: &str) -> Option<houduan_gongyao_shuju> {
        let huihua_map = self.huoye_huihua.lock().unwrap();
        huihua_map.get(huihua_id)
            .and_then(|huihua| huihua.ecdh.chongxian_gongyao_shuju().cloned())
    }

    /// 通过会话ID重现并交换密钥（兼容性方法，仅内存）
    pub fn chongxian_jiaohuan_miyao(&self, huihua_id: &str, duifang_gongyao_hex: &str) -> Result<houduan_gongxiang_miyao> {
        let huihua_map = self.huoye_huihua.lock().unwrap();
        let huihua = huihua_map.get(huihua_id)
            .ok_or_else(|| anyhow!("找不到指定的会话ID: {}", huihua_id))?;

        // 验证对方公钥格式和时间戳
        if !houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi(duifang_gongyao_hex)? {
            return Err(anyhow!("无效的公钥格式或时间戳已过期"));
        }

        // 使用存储的ECDH实例计算共享密钥
        huihua.ecdh.jisuan_gongxiang_miyao(duifang_gongyao_hex)
    }
}

impl Default for miyaojiaohuan_guanliqii {
    fn default() -> Self {
        Self::new()
    }
}

/// HTTP接口：处理密钥交换请求
#[post("/key-exchange", data = "<qingqiu>")]
pub fn miyao_jiaohuan_jiekou(
    qingqiu: Json<miyaojiaohuan_qingqiu>,
    guanliqii: &State<miyaojiaohuan_guanliqii>,
) -> Json<fuwuqi_xiangying> {
    match guanliqii.chuli_miyao_jiaohuan(&qingqiu) {
        Ok(xiangying) => Json(fuwuqi_xiangying {
            chenggong: true,
            xiaoxi: "密钥交换成功".to_string(),
            shuju: Some(serde_json::to_value(xiangying).unwrap()),
        }),
        Err(e) => Json(fuwuqi_xiangying {
            chenggong: false,
            xiaoxi: format!("密钥交换失败: {}", e),
            shuju: None,
        }),
    }
}

/// HTTP接口：查询会话状态
#[get("/session-status/<huihua_id>")]
pub fn huihua_zhuangtai_jiekou(
    huihua_id: &str,
    guanliqii: &State<miyaojiaohuan_guanliqii>,
) -> Json<fuwuqi_xiangying> {
    match guanliqii.get_huihua_zhuangtai(huihua_id) {
        Ok(zhuangtai) => Json(fuwuqi_xiangying {
            chenggong: true,
            xiaoxi: "获取会话状态成功".to_string(),
            shuju: Some(serde_json::to_value(zhuangtai).unwrap()),
        }),
        Err(e) => Json(fuwuqi_xiangying {
            chenggong: false,
            xiaoxi: format!("获取会话状态失败: {}", e),
            shuju: None,
        }),
    }
}

/// 获取所有密钥交换相关的路由
pub fn get_miyao_jiaohuan_routes() -> Vec<Route> {
    routes![miyao_jiaohuan_jiekou, huihua_zhuangtai_jiekou]
}
