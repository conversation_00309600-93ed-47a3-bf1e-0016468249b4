#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use p256::{PublicKey, SecretKey};
use p256::elliptic_curve::sec1::ToEncodedPoint;
use p256::elliptic_curve::ecdh::diffie_hellman;
use p256::elliptic_curve::rand_core::OsRng;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose};
use sha2::{Sha256, Digest};
use chrono::{DateTime, Utc};
use hex;

/// 前端ECDH密钥交换器（适用于WASM）
#[derive(Debug)]
pub struct qianduan_ecdh_miyaojiaohuan {
    /// 私钥
    siyao: SecretKey,
    /// 公钥
    gongyao: PublicKey,
    /// 客户端ID
    kehuduan_id: String,
}

/// 前端公钥交换数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct qianduan_gongyao_shuju {
    /// 小写二进制编码的公钥+时间戳
    pub gongyao: String,
    /// 客户端ID
    pub kehuduan_id: String,
    /// 13位时间戳（毫秒）
    pub shijian_chuo: i64,
    /// 浏览器信息（可选）
    pub liulanqi_xinxi: Option<String>,
}

/// 前端共享密钥结果
#[derive(Debug, Clone)]
pub struct qianduan_gongxiang_miyao {
    /// 原始共享密钥
    pub yuanshi_miyao: Vec<u8>,
    /// 派生的AES密钥
    pub aes_miyao: Vec<u8>,
    /// 客户端ID
    pub kehuduan_id: String,
    /// 服务器会话ID
    pub fuwuqi_huihua_id: Option<String>,
}

/// 密钥交换请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct qianduan_miyao_qingqiu {
    /// 客户端公钥数据
    pub kehuduan_gongyao: qianduan_gongyao_shuju,
    /// 请求类型
    pub qingqiu_leixing: String,
}

/// 密钥交换响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct qianduan_miyao_xiangying {
    /// 服务器公钥数据
    pub fuwuqi_gongyao: String,
    /// 服务器会话ID
    pub fuwuqi_huihua_id: String,
    /// 响应状态
    pub zhuangtai: String,
    /// 错误信息（如果有）
    pub cuowu_xinxi: Option<String>,
}

impl qianduan_ecdh_miyaojiaohuan {
    /// 创建新的前端ECDH密钥交换器
    pub fn new() -> Result<Self> {
        let siyao = SecretKey::random(&mut OsRng);
        let gongyao = siyao.public_key();
        let kehuduan_id = format!("client_{}", uuid::Uuid::new_v4().simple());

        Ok(Self {
            siyao,
            gongyao,
            kehuduan_id,
        })
    }

    /// 创建带自定义客户端ID的密钥交换器
    pub fn new_with_id(kehuduan_id: String) -> Result<Self> {
        let siyao = SecretKey::random(&mut OsRng);
        let gongyao = siyao.public_key();

        Ok(Self {
            siyao,
            gongyao,
            kehuduan_id,
        })
    }

    /// 获取公钥数据用于发送给服务器
    pub fn get_gongyao_shuju(&self) -> Result<qianduan_gongyao_shuju> {
        let gongyao_bytes = self.gongyao.to_encoded_point(false).as_bytes().to_vec();
        let xianzai_shijian = Utc::now();
        let shijian_chuo = xianzai_shijian.timestamp_millis();

        // 将公钥和13位时间戳组合
        let mut gongyao_with_timestamp = gongyao_bytes;
        gongyao_with_timestamp.extend_from_slice(&shijian_chuo.to_be_bytes());

        // 转换为小写二进制字符串
        let gongyao_hex = gongyao_with_timestamp.iter()
            .fold(String::new(), |mut acc, b| {
                use std::fmt::Write;
                write!(&mut acc, "{:02x}", b).unwrap();
                acc
            });

        Ok(qianduan_gongyao_shuju {
            gongyao: gongyao_hex,
            kehuduan_id: self.kehuduan_id.clone(),
            shijian_chuo,
            liulanqi_xinxi: Self::get_liulanqi_xinxi(),
        })
    }

    /// 创建密钥交换请求
    pub fn chuangjian_miyao_qingqiu(&self) -> Result<qianduan_miyao_qingqiu> {
        let kehuduan_gongyao = self.get_gongyao_shuju()?;
        
        Ok(qianduan_miyao_qingqiu {
            kehuduan_gongyao,
            qingqiu_leixing: "ecdh_key_exchange".to_string(),
        })
    }

    /// 处理服务器响应并计算共享密钥
    pub fn chuli_fuwuqi_xiangying(&self, xiangying: &qianduan_miyao_xiangying) -> Result<qianduan_gongxiang_miyao> {
        if xiangying.zhuangtai != "success" {
            return Err(anyhow!("服务器响应错误: {:?}", xiangying.cuowu_xinxi));
        }

        // 解析小写二进制格式的服务器公钥+时间戳
        let fuwuqi_gongyao_with_timestamp_bytes = hex::decode(&xiangying.fuwuqi_gongyao)
            .map_err(|e| anyhow!("解码服务器公钥失败: {}", e))?;

        // 检查长度（65字节公钥 + 8字节时间戳）
        if fuwuqi_gongyao_with_timestamp_bytes.len() != 73 {
            return Err(anyhow!("服务器公钥+时间戳数据长度错误"));
        }

        // 分离公钥和时间戳
        let fuwuqi_gongyao_bytes = &fuwuqi_gongyao_with_timestamp_bytes[..65];
        let shijian_chuo_bytes = &fuwuqi_gongyao_with_timestamp_bytes[65..];

        // 解析时间戳并验证
        let mut timestamp_array = [0u8; 8];
        timestamp_array.copy_from_slice(shijian_chuo_bytes);
        let fuwuqi_shijian_chuo = i64::from_be_bytes(timestamp_array);

        // 验证时间戳（12小时内有效）
        let xianzai_shijian = Utc::now().timestamp_millis();
        let shijian_cha = (xianzai_shijian - fuwuqi_shijian_chuo).abs();
        let max_youxiao_shijian = 12 * 60 * 60 * 1000; // 12小时的毫秒数

        if shijian_cha > max_youxiao_shijian {
            return Err(anyhow!("服务器时间戳已过期，超过12小时限制"));
        }

        // 从字节创建公钥
        let fuwuqi_gongyao = PublicKey::from_sec1_bytes(fuwuqi_gongyao_bytes)
            .map_err(|e| anyhow!("创建服务器公钥失败: {}", e))?;

        // 执行ECDH密钥交换
        let gongxiang_mimi = diffie_hellman(
            self.siyao.to_nonzero_scalar(),
            fuwuqi_gongyao.as_affine(),
        );

        let yuanshi_miyao = gongxiang_mimi.raw_secret_bytes().to_vec();

        // 使用SHA256派生AES密钥，包含客户端ID和服务器会话ID
        let mut hasher = Sha256::new();
        hasher.update(&yuanshi_miyao);
        hasher.update(self.kehuduan_id.as_bytes());
        hasher.update(xiangying.fuwuqi_huihua_id.as_bytes());
        let aes_miyao = hasher.finalize().to_vec();

        Ok(qianduan_gongxiang_miyao {
            yuanshi_miyao,
            aes_miyao,
            kehuduan_id: self.kehuduan_id.clone(),
            fuwuqi_huihua_id: Some(xiangying.fuwuqi_huihua_id.clone()),
        })
    }

    /// 获取客户端ID
    pub fn get_kehuduan_id(&self) -> &str {
        &self.kehuduan_id
    }

    /// 获取浏览器信息（模拟，实际WASM中可以获取真实信息）
    fn get_liulanqi_xinxi() -> Option<String> {
        Some("Rust-WASM-Client/1.0".to_string())
    }

    /// 验证服务器响应格式（小写二进制+时间戳）
    pub fn yanzheng_fuwuqi_xiangying(xiangying: &qianduan_miyao_xiangying) -> Result<bool> {
        if xiangying.zhuangtai != "success" {
            return Ok(false);
        }

        // 验证服务器公钥格式
        match hex::decode(&xiangying.fuwuqi_gongyao) {
            Ok(bytes) => {
                // 检查长度（65字节公钥 + 8字节时间戳）
                if bytes.len() != 73 {
                    return Ok(false);
                }

                // 验证公钥部分
                let gongyao_bytes = &bytes[..65];
                match PublicKey::from_sec1_bytes(gongyao_bytes) {
                    Ok(_) => {
                        // 验证时间戳部分
                        let shijian_chuo_bytes = &bytes[65..];
                        let mut timestamp_array = [0u8; 8];
                        timestamp_array.copy_from_slice(shijian_chuo_bytes);
                        let shijian_chuo = i64::from_be_bytes(timestamp_array);

                        // 检查时间戳是否在合理范围内
                        let xianzai_shijian = Utc::now().timestamp_millis();
                        let shijian_cha = (xianzai_shijian - shijian_chuo).abs();
                        let max_youxiao_shijian = 12 * 60 * 60 * 1000; // 12小时

                        Ok(shijian_cha <= max_youxiao_shijian)
                    },
                    Err(_) => Ok(false),
                }
            }
            Err(_) => Ok(false),
        }
    }
}

impl Default for qianduan_ecdh_miyaojiaohuan {
    fn default() -> Self {
        Self::new().expect("创建前端ECDH密钥交换器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::houduan_ecdh_miyaojiaohuan::*;

    #[test]
    fn test_qianduan_houduan_miyao_jiaohuan() {
        // 创建前端和后端密钥交换器
        let qianduan = qianduan_ecdh_miyaojiaohuan::new().unwrap();
        let houduan = houduan_ecdh_miyaojiaohuan::new().unwrap();

        // 前端创建请求
        let qingqiu = qianduan.chuangjian_miyao_qingqiu().unwrap();
        
        // 后端处理请求并生成响应
        let houduan_gongyao = houduan.get_gongyao_shuju().unwrap();
        let xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: houduan_gongyao.gongyao,
            fuwuqi_huihua_id: houduan_gongyao.huihua_id,
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };

        // 前端处理响应
        let qianduan_gongxiang = qianduan.chuli_fuwuqi_xiangying(&xiangying).unwrap();
        
        // 后端计算共享密钥
        let houduan_gongxiang = houduan.jisuan_gongxiang_miyao(&qingqiu.kehuduan_gongyao.gongyao).unwrap();

        // 验证共享密钥相同
        assert_eq!(qianduan_gongxiang.yuanshi_miyao, houduan_gongxiang.yuanshi_miyao);
        assert_eq!(qianduan_gongxiang.aes_miyao.len(), 32);
    }
}
