#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use p256::{PublicKey, SecretKey};
use p256::elliptic_curve::sec1::ToEncodedPoint;
use p256::elliptic_curve::ecdh::diffie_hellman;
use p256::elliptic_curve::rand_core::OsRng;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose};
use sha2::{Sha256, Digest};
use chrono::{DateTime, Utc};
use hex;
use crate::fuwuqi::jiamichuanshu::jiamichuanshu_rizhi_cuowu;

/// 后端ECDH密钥交换器
#[derive(Debug)]
pub struct houduan_ecdh_miyaojiaohuan {
    /// 私钥
    siyao: SecretKey,
    /// 公钥
    gongyao: PublicKey,
    /// 会话ID
    huihua_id: String,
    /// 公钥创建时间戳
    chuangjian_shijian: DateTime<Utc>,
    /// 存储的公钥数据（用于重现）
    cunchu_gongyao_shuju: Option<houduan_gongyao_shuju>,
}

/// 公钥交换数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct houduan_gongyao_shuju {
    /// 小写二进制编码的公钥+时间戳
    pub gongyao: String,
    /// 会话ID
    pub huihua_id: String,
    /// 13位时间戳（毫秒）
    pub shijian_chuo: i64,
}

/// 共享密钥结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct houduan_gongxiang_miyao {
    /// 原始共享密钥
    pub yuanshi_miyao: Vec<u8>,
    /// 派生的AES密钥
    pub aes_miyao: Vec<u8>,
    /// 会话ID
    pub huihua_id: String,
}

impl houduan_ecdh_miyaojiaohuan {
    /// 创建新的ECDH密钥交换器
    pub fn new() -> Result<Self> {
        let siyao = SecretKey::random(&mut OsRng);
        let gongyao = siyao.public_key();
        let huihua_id = uuid::Uuid::new_v4().to_string();
        let chuangjian_shijian = Utc::now();

        Ok(Self {
            siyao,
            gongyao,
            huihua_id,
            chuangjian_shijian,
            cunchu_gongyao_shuju: None,
        })
    }

    /// 获取公钥数据用于交换
    pub fn get_gongyao_shuju(&self) -> Result<houduan_gongyao_shuju> {
        // 如果已经存储了公钥数据，直接返回
        if let Some(ref cunchu_shuju) = self.cunchu_gongyao_shuju {
            return Ok(cunchu_shuju.clone());
        }

        // 生成新的公钥数据
        let gongyao_bytes = self.gongyao.to_encoded_point(false).as_bytes().to_vec();
        let xianzai_shijian = Utc::now();
        let shijian_chuo = xianzai_shijian.timestamp_millis();

        // 将公钥和13位时间戳组合
        let mut gongyao_with_timestamp = gongyao_bytes;
        gongyao_with_timestamp.extend_from_slice(&shijian_chuo.to_be_bytes());

        // 转换为小写二进制字符串
        let gongyao_hex = gongyao_with_timestamp.iter()
            .fold(String::new(), |mut acc, b| {
                use std::fmt::Write;
                write!(&mut acc, "{:02x}", b).unwrap();
                acc
            });

        Ok(houduan_gongyao_shuju {
            gongyao: gongyao_hex,
            huihua_id: self.huihua_id.clone(),
            shijian_chuo,
        })
    }

    /// 存储公钥数据到内存中
    pub fn cunchu_gongyao_shuju(&mut self, gongyao_shuju: houduan_gongyao_shuju) {
        self.cunchu_gongyao_shuju = Some(gongyao_shuju);
    }

    /// 从会话ID重现公钥数据
    pub fn chongxian_gongyao_shuju(&self) -> Option<&houduan_gongyao_shuju> {
        self.cunchu_gongyao_shuju.as_ref()
    }

    /// 计算共享密钥
    pub fn jisuan_gongxiang_miyao(&self, duifang_gongyao_hex: &str) -> Result<houduan_gongxiang_miyao> {
        self.jisuan_gongxiang_miyao_with_kehuduan_id(duifang_gongyao_hex, None)
    }

    /// 计算共享密钥（带客户端ID）
    pub fn jisuan_gongxiang_miyao_with_kehuduan_id(&self, duifang_gongyao_hex: &str, kehuduan_id: Option<&str>) -> Result<houduan_gongxiang_miyao> {
        // 解析小写二进制格式的公钥+时间戳
        let gongyao_with_timestamp_bytes = hex::decode(duifang_gongyao_hex)
            .map_err(|e| {
                jiamichuanshu_rizhi_cuowu("密钥模块", "解码对方公钥失败");
                anyhow!("解码对方公钥失败: {}", e)
            })?;

        // 检查长度（65字节公钥 + 8字节时间戳）
        if gongyao_with_timestamp_bytes.len() != 73 {
            return Err(anyhow!("公钥+时间戳数据长度错误，期望73字节，实际{}字节", gongyao_with_timestamp_bytes.len()));
        }

        // 分离公钥和时间戳
        let duifang_gongyao_bytes = &gongyao_with_timestamp_bytes[..65];
        let shijian_chuo_bytes = &gongyao_with_timestamp_bytes[65..];

        // 解析时间戳
        let mut timestamp_array = [0u8; 8];
        timestamp_array.copy_from_slice(shijian_chuo_bytes);
        let duifang_shijian_chuo = i64::from_be_bytes(timestamp_array);

        // 验证时间戳（12小时内有效）
        let xianzai_shijian = Utc::now().timestamp_millis();
        let shijian_cha = (xianzai_shijian - duifang_shijian_chuo).abs();
        let max_youxiao_shijian = 12 * 60 * 60 * 1000; // 12小时的毫秒数

        if shijian_cha > max_youxiao_shijian {
            return Err(anyhow!("时间戳已过期，超过12小时限制。时间差: {}毫秒", shijian_cha));
        }

        // 从字节创建公钥
        let duifang_gongyao = PublicKey::from_sec1_bytes(duifang_gongyao_bytes)
            .map_err(|e| anyhow!("创建对方公钥失败: {}", e))?;

        // 执行ECDH密钥交换
        let gongxiang_mimi = diffie_hellman(
            self.siyao.to_nonzero_scalar(),
            duifang_gongyao.as_affine(),
        );

        let yuanshi_miyao = gongxiang_mimi.raw_secret_bytes().to_vec();

        // 使用SHA256派生AES密钥，包含客户端ID和服务器会话ID
        let mut hasher = Sha256::new();
        hasher.update(&yuanshi_miyao);
        if let Some(kehuduan_id) = kehuduan_id {
            hasher.update(kehuduan_id.as_bytes());
        }
        hasher.update(self.huihua_id.as_bytes());
        let aes_miyao = hasher.finalize().to_vec();

        Ok(houduan_gongxiang_miyao {
            yuanshi_miyao,
            aes_miyao,
            huihua_id: self.huihua_id.clone(),
        })
    }

    /// 获取会话ID
    pub fn get_huihua_id(&self) -> &str {
        &self.huihua_id
    }

    /// 获取私钥的hex编码
    pub fn get_siyao_hex(&self) -> String {
        hex::encode(self.siyao.to_bytes())
    }

    /// 从私钥hex编码恢复
    pub fn cong_siyao_hex_huifu(&mut self, siyao_hex: &str) -> Result<()> {
        let siyao_bytes = hex::decode(siyao_hex)
            .map_err(|e| anyhow!("解码私钥失败: {}", e))?;

        if siyao_bytes.len() != 32 {
            return Err(anyhow!("私钥长度错误，期望32字节，实际{}字节", siyao_bytes.len()));
        }

        let mut key_array = [0u8; 32];
        key_array.copy_from_slice(&siyao_bytes);

        self.siyao = SecretKey::from_bytes(&key_array.into())
            .map_err(|e| anyhow!("从字节创建私钥失败: {}", e))?;
        self.gongyao = self.siyao.public_key();

        Ok(())
    }

    /// 使用指定会话ID创建ECDH实例
    pub fn new_with_id(huihua_id: &str) -> Result<Self> {
        let siyao = SecretKey::random(&mut OsRng);
        let gongyao = siyao.public_key();
        let chuangjian_shijian = Utc::now();

        Ok(Self {
            siyao,
            gongyao,
            huihua_id: huihua_id.to_string(),
            chuangjian_shijian,
            cunchu_gongyao_shuju: None,
        })
    }

    /// 验证公钥格式（小写二进制+时间戳）
    pub fn yanzheng_gongyao_geshi(gongyao_hex: &str) -> Result<bool> {
        match hex::decode(gongyao_hex) {
            Ok(bytes) => {
                // 检查长度（65字节公钥 + 8字节时间戳）
                if bytes.len() != 73 {
                    return Ok(false);
                }

                // 验证公钥部分
                let gongyao_bytes = &bytes[..65];
                match PublicKey::from_sec1_bytes(gongyao_bytes) {
                    Ok(_) => {
                        // 验证时间戳部分
                        let shijian_chuo_bytes = &bytes[65..];
                        let mut timestamp_array = [0u8; 8];
                        timestamp_array.copy_from_slice(shijian_chuo_bytes);
                        let shijian_chuo = i64::from_be_bytes(timestamp_array);

                        // 检查时间戳是否在合理范围内
                        let xianzai_shijian = Utc::now().timestamp_millis();
                        let shijian_cha = (xianzai_shijian - shijian_chuo).abs();
                        let max_youxiao_shijian = 12 * 60 * 60 * 1000; // 12小时

                        Ok(shijian_cha <= max_youxiao_shijian)
                    },
                    Err(_) => Ok(false),
                }
            }
            Err(_) => Ok(false),
        }
    }
}

impl Default for houduan_ecdh_miyaojiaohuan {
    fn default() -> Self {
        Self::new().expect("创建ECDH密钥交换器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ecdh_miyaojiaohuan() {
        // 创建两个密钥交换器
        let alice = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let bob = houduan_ecdh_miyaojiaohuan::new().unwrap();

        // 获取公钥数据
        let alice_gongyao = alice.get_gongyao_shuju().unwrap();
        let bob_gongyao = bob.get_gongyao_shuju().unwrap();

        // 计算共享密钥
        let alice_gongxiang = alice.jisuan_gongxiang_miyao(&bob_gongyao.gongyao).unwrap();
        let bob_gongxiang = bob.jisuan_gongxiang_miyao(&alice_gongyao.gongyao).unwrap();

        // 验证共享密钥相同
        assert_eq!(alice_gongxiang.yuanshi_miyao, bob_gongxiang.yuanshi_miyao);
        assert_eq!(alice_gongxiang.aes_miyao.len(), 32); // SHA256输出32字节
    }

    #[test]
    fn test_gongyao_yanzheng() {
        let ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let gongyao_shuju = ecdh.get_gongyao_shuju().unwrap();
        
        // 验证有效公钥
        assert!(houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi(&gongyao_shuju.gongyao).unwrap());
        
        // 验证无效公钥
        assert!(!houduan_ecdh_miyaojiaohuan::yanzheng_gongyao_geshi("invalid_key").unwrap());
    }
}
