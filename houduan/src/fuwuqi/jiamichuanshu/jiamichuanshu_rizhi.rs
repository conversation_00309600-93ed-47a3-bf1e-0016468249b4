use crate::rizhixitong;

/// 加密传输系统信息日志
pub fn jiamichuanshu_rizhi_xinxi(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("加密传输模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 加密传输系统警告日志
pub fn jiamichuanshu_rizhi_jinggao(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("加密传输模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
}

/// 加密传输系统错误日志
pub fn jiamichuanshu_rizhi_cuowu(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("加密传输模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_cuowu(&rizhi_xiaoxi);
}

/// 加密传输系统调试日志
pub fn jiamichuanshu_rizhi_tiaoshi(mokuai_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("加密传输模块中的{}模块：{}", mokuai_ming, xiaoxi);
    rizhixitong::rizhixitong_tiaoshi(&rizhi_xiaoxi);
}

/// 兼容性函数：根据消息内容自动判断日志级别
/// 建议使用具体的级别函数替代此函数
pub fn jiamichuanshu_rizhi(mokuai_ming: &str, xiaoxi: &str) {
    let xiaoxi_xiaoxie = xiaoxi.to_lowercase();

    // 判断是否为错误信息（加密传输特定错误）
    if xiaoxi_xiaoxie.contains("失败") || xiaoxi_xiaoxie.contains("错误") ||
       xiaoxi_xiaoxie.contains("异常") || xiaoxi_xiaoxie.contains("error") ||
       xiaoxi_xiaoxie.contains("failed") || xiaoxi_xiaoxie.contains("exception") ||
       xiaoxi_xiaoxie.contains("密钥交换失败") || xiaoxi_xiaoxie.contains("加密失败") ||
       xiaoxi_xiaoxie.contains("解密失败") || xiaoxi_xiaoxie.contains("签名验证失败") ||
       xiaoxi_xiaoxie.contains("连接超时") || xiaoxi_xiaoxie.contains("握手失败") {
        jiamichuanshu_rizhi_cuowu(mokuai_ming, xiaoxi);
    }
    // 判断是否为警告信息（加密传输特定警告）
    else if xiaoxi_xiaoxie.contains("警告") || xiaoxi_xiaoxie.contains("warn") ||
            xiaoxi_xiaoxie.contains("注意") || xiaoxi_xiaoxie.contains("超时") ||
            xiaoxi_xiaoxie.contains("密钥即将过期") || xiaoxi_xiaoxie.contains("连接不稳定") ||
            xiaoxi_xiaoxie.contains("性能警告") || xiaoxi_xiaoxie.contains("重试") ||
            xiaoxi_xiaoxie.contains("降级") {
        jiamichuanshu_rizhi_jinggao(mokuai_ming, xiaoxi);
    }
    // 判断是否为调试信息
    else if xiaoxi_xiaoxie.contains("调试") || xiaoxi_xiaoxie.contains("debug") ||
            xiaoxi_xiaoxie.contains("trace") || xiaoxi_xiaoxie.contains("详细") ||
            xiaoxi_xiaoxie.contains("密钥生成") || xiaoxi_xiaoxie.contains("协议协商") {
        jiamichuanshu_rizhi_tiaoshi(mokuai_ming, xiaoxi);
    }
    // 其他情况作为信息日志
    else {
        jiamichuanshu_rizhi_xinxi(mokuai_ming, xiaoxi);
    }
}