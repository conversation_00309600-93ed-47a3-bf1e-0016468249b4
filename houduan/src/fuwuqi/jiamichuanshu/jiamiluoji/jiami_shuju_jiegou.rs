#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose};

/// 加密数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct jiami_shuju {
    /// 加密后的数据（base64编码）
    pub miwen: String,
    /// 13位毫秒时间戳
    pub shijian_chuo: i64,
    /// 数据完整性签名（SHA256哈希，base64编码）
    pub qianming: String,
    /// 加密算法标识
    pub jiami_suanfa: String,
    /// 初始化向量（IV，base64编码）
    pub chushiping_xiangliang: String,
    /// 认证标签（GCM模式，base64编码）
    pub renzheng_biaoqian: String,
}

/// 解密后的数据结构
#[derive(Debug, Clone)]
pub struct jiemi_shuju {
    /// 原始数据
    pub yuanshi_shuju: Vec<u8>,
    /// 时间戳
    pub shijian_chuo: i64,
    /// 签名验证结果
    pub qianming_youxiao: bool,
    /// 时间戳验证结果
    pub shijian_youxiao: bool,
}

/// 加密参数结构
#[derive(Debug, Clone)]
pub struct jiami_canshu {
    /// AES密钥
    pub aes_miyao: Vec<u8>,
    /// 签名密钥（用于HMAC）
    pub qianming_miyao: Vec<u8>,
    /// 时间戳有效期（秒）
    pub shijian_youxiaoqi: u64,
}

impl jiami_shuju {
    /// 创建新的加密数据结构
    pub fn new(
        miwen: Vec<u8>,
        shijian_chuo: i64,
        qianming: Vec<u8>,
        chushiping_xiangliang: Vec<u8>,
        renzheng_biaoqian: Vec<u8>,
    ) -> Self {
        Self {
            miwen: general_purpose::STANDARD.encode(&miwen),
            shijian_chuo,
            qianming: general_purpose::STANDARD.encode(&qianming),
            jiami_suanfa: "AES-256-GCM".to_string(),
            chushiping_xiangliang: general_purpose::STANDARD.encode(&chushiping_xiangliang),
            renzheng_biaoqian: general_purpose::STANDARD.encode(&renzheng_biaoqian),
        }
    }

    /// 获取密文字节数据
    pub fn get_miwen_bytes(&self) -> Result<Vec<u8>> {
        general_purpose::STANDARD.decode(&self.miwen)
            .map_err(|e| anyhow!("解码密文失败: {}", e))
    }

    /// 获取签名字节数据
    pub fn get_qianming_bytes(&self) -> Result<Vec<u8>> {
        general_purpose::STANDARD.decode(&self.qianming)
            .map_err(|e| anyhow!("解码签名失败: {}", e))
    }

    /// 获取初始化向量字节数据
    pub fn get_iv_bytes(&self) -> Result<Vec<u8>> {
        general_purpose::STANDARD.decode(&self.chushiping_xiangliang)
            .map_err(|e| anyhow!("解码IV失败: {}", e))
    }

    /// 获取认证标签字节数据
    pub fn get_tag_bytes(&self) -> Result<Vec<u8>> {
        general_purpose::STANDARD.decode(&self.renzheng_biaoqian)
            .map_err(|e| anyhow!("解码认证标签失败: {}", e))
    }

    /// 转换为JSON字符串
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self)
            .map_err(|e| anyhow!("序列化为JSON失败: {}", e))
    }

    /// 从JSON字符串创建
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json)
            .map_err(|e| anyhow!("从JSON反序列化失败: {}", e))
    }

    /// 转换为二进制格式（用于网络传输）
    pub fn to_binary(&self) -> Result<Vec<u8>> {
        bincode::serialize(self)
            .map_err(|e| anyhow!("序列化为二进制失败: {}", e))
    }

    /// 从二进制格式创建
    pub fn from_binary(data: &[u8]) -> Result<Self> {
        bincode::deserialize(data)
            .map_err(|e| anyhow!("从二进制反序列化失败: {}", e))
    }

    /// 验证数据结构完整性
    pub fn validate(&self) -> Result<()> {
        // 检查必要字段
        if self.miwen.is_empty() {
            return Err(anyhow!("密文不能为空"));
        }
        if self.qianming.is_empty() {
            return Err(anyhow!("签名不能为空"));
        }
        if self.chushiping_xiangliang.is_empty() {
            return Err(anyhow!("初始化向量不能为空"));
        }
        if self.renzheng_biaoqian.is_empty() {
            return Err(anyhow!("认证标签不能为空"));
        }

        // 检查时间戳格式（13位毫秒）
        if self.shijian_chuo.to_string().len() != 13 {
            return Err(anyhow!("时间戳必须是13位毫秒格式"));
        }

        // 检查算法标识
        if self.jiami_suanfa != "AES-256-GCM" {
            return Err(anyhow!("不支持的加密算法: {}", self.jiami_suanfa));
        }

        // 验证base64编码格式
        self.get_miwen_bytes()?;
        self.get_qianming_bytes()?;
        self.get_iv_bytes()?;
        self.get_tag_bytes()?;

        Ok(())
    }
}

impl jiemi_shuju {
    /// 创建新的解密数据结构
    pub fn new(
        yuanshi_shuju: Vec<u8>,
        shijian_chuo: i64,
        qianming_youxiao: bool,
        shijian_youxiao: bool,
    ) -> Self {
        Self {
            yuanshi_shuju,
            shijian_chuo,
            qianming_youxiao,
            shijian_youxiao,
        }
    }

    /// 转换为字符串（UTF-8）
    pub fn to_string(&self) -> Result<String> {
        String::from_utf8(self.yuanshi_shuju.clone())
            .map_err(|e| anyhow!("转换为UTF-8字符串失败: {}", e))
    }

    /// 检查数据是否有效
    pub fn is_valid(&self) -> bool {
        self.qianming_youxiao && self.shijian_youxiao
    }
}

impl jiami_canshu {
    /// 创建新的加密参数
    pub fn new(aes_miyao: Vec<u8>, qianming_miyao: Vec<u8>) -> Self {
        Self {
            aes_miyao,
            qianming_miyao,
            shijian_youxiaoqi: 300, // 默认5分钟有效期
        }
    }

    /// 设置时间戳有效期
    pub fn with_timeout(mut self, timeout_seconds: u64) -> Self {
        self.shijian_youxiaoqi = timeout_seconds;
        self
    }

    /// 验证密钥长度
    pub fn validate(&self) -> Result<()> {
        if self.aes_miyao.len() != 32 {
            return Err(anyhow!("AES密钥长度必须是32字节"));
        }
        if self.qianming_miyao.len() != 32 {
            return Err(anyhow!("签名密钥长度必须是32字节"));
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jiami_shuju_creation() {
        let miwen = b"encrypted_data".to_vec();
        let qianming = b"signature_hash".to_vec();
        let iv = b"initialization_v".to_vec();
        let tag = b"auth_tag".to_vec();
        let timestamp = 1640995200000i64; // 13位时间戳

        let jiami_data = jiami_shuju::new(miwen, timestamp, qianming, iv, tag);
        
        assert_eq!(jiami_data.shijian_chuo, timestamp);
        assert_eq!(jiami_data.jiami_suanfa, "AES-256-GCM");
        assert!(jiami_data.validate().is_ok());
    }

    #[test]
    fn test_jiami_shuju_serialization() {
        let miwen = b"test_data".to_vec();
        let qianming = b"test_sig".to_vec();
        let iv = b"test_iv_12345678".to_vec();
        let tag = b"test_tag".to_vec();
        let timestamp = 1640995200000i64;

        let jiami_data = jiami_shuju::new(miwen, timestamp, qianming, iv, tag);
        
        // 测试JSON序列化
        let json = jiami_data.to_json().unwrap();
        let restored = jiami_shuju::from_json(&json).unwrap();
        assert_eq!(jiami_data.shijian_chuo, restored.shijian_chuo);

        // 测试二进制序列化
        let binary = jiami_data.to_binary().unwrap();
        let restored_bin = jiami_shuju::from_binary(&binary).unwrap();
        assert_eq!(jiami_data.shijian_chuo, restored_bin.shijian_chuo);
    }

    #[test]
    fn test_jiami_canshu_validation() {
        let aes_key = vec![0u8; 32]; // 32字节AES密钥
        let sign_key = vec![1u8; 32]; // 32字节签名密钥
        
        let params = jiami_canshu::new(aes_key, sign_key);
        assert!(params.validate().is_ok());

        // 测试错误的密钥长度
        let wrong_aes = vec![0u8; 16]; // 错误长度
        let params_wrong = jiami_canshu::new(wrong_aes, vec![1u8; 32]);
        assert!(params_wrong.validate().is_err());
    }
}
