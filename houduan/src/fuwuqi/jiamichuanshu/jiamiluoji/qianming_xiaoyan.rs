#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use sha2::{Sha256, Digest};
use anyhow::{Result, anyhow};
use hex;

/// 签名校验器
#[derive(Debug)]
pub struct qianming_xiaoyangqi {
    /// 签名密钥
    qianming_miyao: Vec<u8>,
    /// 签名算法标识
    suanfa_biaoshi: String,
}

/// 签名数据结构
#[derive(Debug, Clone)]
pub struct qianming_shuju {
    /// 原始数据
    pub yuanshi_shuju: Vec<u8>,
    /// 时间戳
    pub shijian_chuo: i64,
    /// 密钥信息（可选）
    pub miyao_xinxi: Option<String>,
    /// 附加数据（可选）
    pub fujia_shuju: Option<Vec<u8>>,
}

/// 签名验证结果
#[derive(Debug, <PERSON>lone)]
pub struct qianming_yanzheng_jieguo {
    /// 验证是否成功
    pub chenggong: bool,
    /// 计算的签名
    pub jisuan_qianming: Vec<u8>,
    /// 原始签名
    pub yuanshi_qianming: Vec<u8>,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 签名算法
    pub suanfa: String,
}

impl qianming_xiaoyangqi {
    /// 创建新的签名校验器
    pub fn new(qianming_miyao: Vec<u8>) -> Result<Self> {
        if qianming_miyao.len() != 32 {
            return Err(anyhow!("签名密钥长度必须是32字节"));
        }

        Ok(Self {
            qianming_miyao,
            suanfa_biaoshi: "HMAC-SHA256".to_string(),
        })
    }

    /// 生成数据签名
    pub fn shengcheng_qianming(&self, shuju: &qianming_shuju) -> Result<Vec<u8>> {
        let mut hasher = Sha256::new();
        
        // 添加签名密钥
        hasher.update(&self.qianming_miyao);
        
        // 添加原始数据
        hasher.update(&shuju.yuanshi_shuju);
        
        // 添加时间戳（转换为字节）
        hasher.update(shuju.shijian_chuo.to_le_bytes());
        
        // 添加密钥信息（如果有）
        if let Some(ref miyao_xinxi) = shuju.miyao_xinxi {
            hasher.update(miyao_xinxi.as_bytes());
        }
        
        // 添加附加数据（如果有）
        if let Some(ref fujia_shuju) = shuju.fujia_shuju {
            hasher.update(fujia_shuju);
        }
        
        // 添加算法标识
        hasher.update(self.suanfa_biaoshi.as_bytes());
        
        Ok(hasher.finalize().to_vec())
    }

    /// 验证数据签名
    pub fn yanzheng_qianming(
        &self,
        shuju: &qianming_shuju,
        qianming: &[u8],
    ) -> qianming_yanzheng_jieguo {
        match self.shengcheng_qianming(shuju) {
            Ok(jisuan_qianming) => {
                let chenggong = jisuan_qianming == qianming;
                qianming_yanzheng_jieguo {
                    chenggong,
                    jisuan_qianming,
                    yuanshi_qianming: qianming.to_vec(),
                    cuowu_xinxi: if chenggong {
                        None
                    } else {
                        Some("签名验证失败，数据可能被篡改".to_string())
                    },
                    suanfa: self.suanfa_biaoshi.clone(),
                }
            }
            Err(e) => qianming_yanzheng_jieguo {
                chenggong: false,
                jisuan_qianming: Vec::new(),
                yuanshi_qianming: qianming.to_vec(),
                cuowu_xinxi: Some(format!("生成签名失败: {}", e)),
                suanfa: self.suanfa_biaoshi.clone(),
            },
        }
    }

    /// 为加密数据生成签名
    pub fn wei_jiami_shuju_qianming(
        &self,
        miwen: &[u8],
        shijian_chuo: i64,
        iv: &[u8],
        tag: &[u8],
        miyao_xinxi: Option<&str>,
    ) -> Result<Vec<u8>> {
        let qianming_shuju = qianming_shuju {
            yuanshi_shuju: [miwen, iv, tag].concat(),
            shijian_chuo,
            miyao_xinxi: miyao_xinxi.map(|s| s.to_string()),
            fujia_shuju: None,
        };
        
        self.shengcheng_qianming(&qianming_shuju)
    }

    /// 验证加密数据签名
    pub fn yanzheng_jiami_shuju_qianming(
        &self,
        miwen: &[u8],
        shijian_chuo: i64,
        iv: &[u8],
        tag: &[u8],
        qianming: &[u8],
        miyao_xinxi: Option<&str>,
    ) -> qianming_yanzheng_jieguo {
        let qianming_shuju = qianming_shuju {
            yuanshi_shuju: [miwen, iv, tag].concat(),
            shijian_chuo,
            miyao_xinxi: miyao_xinxi.map(|s| s.to_string()),
            fujia_shuju: None,
        };
        
        self.yanzheng_qianming(&qianming_shuju, qianming)
    }

    /// 获取签名密钥的哈希（用于调试）
    pub fn get_miyao_hash(&self) -> String {
        let mut hasher = Sha256::new();
        hasher.update(&self.qianming_miyao);
        hex::encode(hasher.finalize())
    }

    /// 更新签名密钥
    pub fn gengxin_miyao(&mut self, xin_miyao: Vec<u8>) -> Result<()> {
        if xin_miyao.len() != 32 {
            return Err(anyhow!("新签名密钥长度必须是32字节"));
        }
        self.qianming_miyao = xin_miyao;
        Ok(())
    }
}

impl qianming_shuju {
    /// 创建新的签名数据
    pub fn new(yuanshi_shuju: Vec<u8>, shijian_chuo: i64) -> Self {
        Self {
            yuanshi_shuju,
            shijian_chuo,
            miyao_xinxi: None,
            fujia_shuju: None,
        }
    }

    /// 添加密钥信息
    pub fn with_miyao_xinxi(mut self, miyao_xinxi: String) -> Self {
        self.miyao_xinxi = Some(miyao_xinxi);
        self
    }

    /// 添加附加数据
    pub fn with_fujia_shuju(mut self, fujia_shuju: Vec<u8>) -> Self {
        self.fujia_shuju = Some(fujia_shuju);
        self
    }

    /// 获取数据总大小
    pub fn get_shuju_daxiao(&self) -> usize {
        let mut size = self.yuanshi_shuju.len() + 8; // 8字节时间戳
        if let Some(ref miyao_xinxi) = self.miyao_xinxi {
            size += miyao_xinxi.len();
        }
        if let Some(ref fujia_shuju) = self.fujia_shuju {
            size += fujia_shuju.len();
        }
        size
    }
}

/// 签名工具函数
pub struct qianming_gongju;

impl qianming_gongju {
    /// 快速生成数据的SHA256哈希
    pub fn kuaisu_hash(shuju: &[u8]) -> Vec<u8> {
        let mut hasher = Sha256::new();
        hasher.update(shuju);
        hasher.finalize().to_vec()
    }

    /// 生成带时间戳的数据哈希
    pub fn hash_with_timestamp(shuju: &[u8], shijian_chuo: i64) -> Vec<u8> {
        let mut hasher = Sha256::new();
        hasher.update(shuju);
        hasher.update(shijian_chuo.to_le_bytes());
        hasher.finalize().to_vec()
    }

    /// 验证两个哈希是否相等
    pub fn yanzheng_hash_xiangdeng(hash1: &[u8], hash2: &[u8]) -> bool {
        hash1.len() == hash2.len() && hash1.iter().zip(hash2.iter()).all(|(a, b)| a == b)
    }

    /// 将哈希转换为十六进制字符串
    pub fn hash_to_hex(hash: &[u8]) -> String {
        hex::encode(hash)
    }

    /// 从十六进制字符串解析哈希
    pub fn hex_to_hash(hex_str: &str) -> Result<Vec<u8>> {
        hex::decode(hex_str).map_err(|e| anyhow!("解析十六进制哈希失败: {}", e))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_qianming_xiaoyangqi_creation() {
        let miyao = vec![0u8; 32];
        let xiaoyangqi = qianming_xiaoyangqi::new(miyao).unwrap();
        assert_eq!(xiaoyangqi.suanfa_biaoshi, "HMAC-SHA256");

        // 测试错误的密钥长度
        let wrong_miyao = vec![0u8; 16];
        assert!(qianming_xiaoyangqi::new(wrong_miyao).is_err());
    }

    #[test]
    fn test_qianming_shengcheng_he_yanzheng() {
        let miyao = vec![1u8; 32];
        let xiaoyangqi = qianming_xiaoyangqi::new(miyao).unwrap();
        
        let shuju = qianming_shuju::new(
            b"test data".to_vec(),
            1640995200000i64,
        );
        
        // 生成签名
        let qianming = xiaoyangqi.shengcheng_qianming(&shuju).unwrap();
        assert_eq!(qianming.len(), 32); // SHA256输出32字节
        
        // 验证签名
        let yanzheng_jieguo = xiaoyangqi.yanzheng_qianming(&shuju, &qianming);
        assert!(yanzheng_jieguo.chenggong);
        assert!(yanzheng_jieguo.cuowu_xinxi.is_none());
        
        // 测试错误的签名
        let cuowu_qianming = vec![0u8; 32];
        let cuowu_jieguo = xiaoyangqi.yanzheng_qianming(&shuju, &cuowu_qianming);
        assert!(!cuowu_jieguo.chenggong);
        assert!(cuowu_jieguo.cuowu_xinxi.is_some());
    }

    #[test]
    fn test_jiami_shuju_qianming() {
        let miyao = vec![2u8; 32];
        let xiaoyangqi = qianming_xiaoyangqi::new(miyao).unwrap();
        
        let miwen = b"encrypted_data".to_vec();
        let iv = b"initialization_v".to_vec();
        let tag = b"auth_tag".to_vec();
        let shijian_chuo = 1640995200000i64;
        
        // 生成加密数据签名
        let qianming = xiaoyangqi.wei_jiami_shuju_qianming(
            &miwen,
            shijian_chuo,
            &iv,
            &tag,
            Some("test_key_info"),
        ).unwrap();
        
        // 验证加密数据签名
        let yanzheng_jieguo = xiaoyangqi.yanzheng_jiami_shuju_qianming(
            &miwen,
            shijian_chuo,
            &iv,
            &tag,
            &qianming,
            Some("test_key_info"),
        );
        
        assert!(yanzheng_jieguo.chenggong);
    }

    #[test]
    fn test_qianming_gongju() {
        let shuju = b"test data";
        let hash = qianming_gongju::kuaisu_hash(shuju);
        assert_eq!(hash.len(), 32);
        
        // 测试带时间戳的哈希
        let timestamp = 1640995200000i64;
        let hash_with_time = qianming_gongju::hash_with_timestamp(shuju, timestamp);
        assert_eq!(hash_with_time.len(), 32);
        assert_ne!(hash, hash_with_time); // 应该不同
        
        // 测试哈希比较
        assert!(qianming_gongju::yanzheng_hash_xiangdeng(&hash, &hash));
        assert!(!qianming_gongju::yanzheng_hash_xiangdeng(&hash, &hash_with_time));
        
        // 测试十六进制转换
        let hex_str = qianming_gongju::hash_to_hex(&hash);
        let restored_hash = qianming_gongju::hex_to_hash(&hex_str).unwrap();
        assert_eq!(hash, restored_hash);
    }

    #[test]
    fn test_qianming_shuju_with_options() {
        let shuju = qianming_shuju::new(b"test".to_vec(), 1640995200000i64)
            .with_miyao_xinxi("key_info".to_string())
            .with_fujia_shuju(b"extra".to_vec());
        
        assert!(shuju.miyao_xinxi.is_some());
        assert!(shuju.fujia_shuju.is_some());
        assert!(shuju.get_shuju_daxiao() > 4); // 基础数据4字节 + 时间戳8字节 + 额外数据
    }
}
