#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::collections::HashSet;
use std::sync::{Arc, Mutex};
use chrono::{DateTime, Utc, TimeZone};
use anyhow::{Result, anyhow};

/// 时间戳管理器
#[derive(Debug)]
pub struct shijian_chuo_guanliqii {
    /// 已使用的时间戳集合（防重放）
    yishiyong_shijian_chuo: Arc<Mutex<HashSet<i64>>>,
    /// 时间窗口大小（秒）
    shijian_chuangkou: u64,
    /// 最大存储的时间戳数量
    zuida_cunchu_shuliang: usize,
}

/// 时间戳验证结果
#[derive(Debug, Clone)]
pub struct shijian_chuo_yanzheng_jieguo {
    /// 时间戳是否有效
    pub youxiao: bool,
    /// 时间戳值
    pub shijian_chuo: i64,
    /// 验证时间
    pub yanzheng_shijian: DateTime<Utc>,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 时间差（毫秒）
    pub shijian_cha: i64,
}

impl shijian_chuo_guanliqii {
    /// 创建新的时间戳管理器
    pub fn new() -> Self {
        Self {
            yishiyong_shijian_chuo: Arc::new(Mutex::new(HashSet::new())),
            shijian_chuangkou: 300, // 默认5分钟时间窗口
            zuida_cunchu_shuliang: 10000, // 最大存储1万个时间戳
        }
    }

    /// 创建带自定义参数的时间戳管理器
    pub fn new_with_config(shijian_chuangkou: u64, zuida_cunchu_shuliang: usize) -> Self {
        Self {
            yishiyong_shijian_chuo: Arc::new(Mutex::new(HashSet::new())),
            shijian_chuangkou,
            zuida_cunchu_shuliang,
        }
    }

    /// 生成当前13位毫秒时间戳
    pub fn shengcheng_shijian_chuo() -> i64 {
        Utc::now().timestamp_millis()
    }

    /// 验证时间戳有效性
    pub fn yanzheng_shijian_chuo(&self, shijian_chuo: i64) -> shijian_chuo_yanzheng_jieguo {
        let dangqian_shijian = Utc::now();
        let dangqian_haomiao = dangqian_shijian.timestamp_millis();

        // 检查时间戳格式（必须是13位）
        if shijian_chuo.to_string().len() != 13 {
            return shijian_chuo_yanzheng_jieguo {
                youxiao: false,
                shijian_chuo,
                yanzheng_shijian: dangqian_shijian,
                cuowu_xinxi: Some("时间戳必须是13位毫秒格式".to_string()),
                shijian_cha: 0,
            };
        }

        let shijian_cha = (dangqian_haomiao - shijian_chuo).abs();
        let shijian_chuangkou_haomiao = (self.shijian_chuangkou * 1000) as i64;

        // 检查时间窗口
        if shijian_cha > shijian_chuangkou_haomiao {
            return shijian_chuo_yanzheng_jieguo {
                youxiao: false,
                shijian_chuo,
                yanzheng_shijian: dangqian_shijian,
                cuowu_xinxi: Some(format!(
                    "时间戳超出有效窗口，时间差: {}毫秒，允许范围: {}毫秒",
                    shijian_cha, shijian_chuangkou_haomiao
                )),
                shijian_cha,
            };
        }

        // 检查是否重放攻击
        let mut yishiyong_set = self.yishiyong_shijian_chuo.lock().unwrap();
        if yishiyong_set.contains(&shijian_chuo) {
            return shijian_chuo_yanzheng_jieguo {
                youxiao: false,
                shijian_chuo,
                yanzheng_shijian: dangqian_shijian,
                cuowu_xinxi: Some("检测到重放攻击，时间戳已被使用".to_string()),
                shijian_cha,
            };
        }

        // 添加到已使用集合
        yishiyong_set.insert(shijian_chuo);

        // 清理过期的时间戳
        self.qingli_guoqi_shijian_chuo(&mut yishiyong_set, dangqian_haomiao);

        shijian_chuo_yanzheng_jieguo {
            youxiao: true,
            shijian_chuo,
            yanzheng_shijian: dangqian_shijian,
            cuowu_xinxi: None,
            shijian_cha,
        }
    }

    /// 清理过期的时间戳
    fn qingli_guoqi_shijian_chuo(&self, yishiyong_set: &mut HashSet<i64>, dangqian_haomiao: i64) {
        let shijian_chuangkou_haomiao = (self.shijian_chuangkou * 1000) as i64;
        
        // 如果数量超过限制，清理过期的时间戳
        if yishiyong_set.len() > self.zuida_cunchu_shuliang {
            yishiyong_set.retain(|&timestamp| {
                (dangqian_haomiao - timestamp).abs() <= shijian_chuangkou_haomiao
            });
        }
    }

    /// 强制清理所有时间戳
    pub fn qingli_suoyou_shijian_chuo(&self) {
        let mut yishiyong_set = self.yishiyong_shijian_chuo.lock().unwrap();
        yishiyong_set.clear();
    }

    /// 获取已存储的时间戳数量
    pub fn get_cunchu_shuliang(&self) -> usize {
        let yishiyong_set = self.yishiyong_shijian_chuo.lock().unwrap();
        yishiyong_set.len()
    }

    /// 设置时间窗口大小
    pub fn set_shijian_chuangkou(&mut self, chuangkou_miao: u64) {
        self.shijian_chuangkou = chuangkou_miao;
    }

    /// 获取时间窗口大小
    pub fn get_shijian_chuangkou(&self) -> u64 {
        self.shijian_chuangkou
    }
}

/// 时间戳工具函数
pub struct shijian_chuo_gongju;

impl shijian_chuo_gongju {
    /// 将13位时间戳转换为DateTime
    pub fn timestamp_to_datetime(timestamp: i64) -> Result<DateTime<Utc>> {
        if timestamp.to_string().len() != 13 {
            return Err(anyhow!("时间戳必须是13位毫秒格式"));
        }

        Utc.timestamp_millis_opt(timestamp)
            .single()
            .ok_or_else(|| anyhow!("无效的时间戳: {}", timestamp))
    }

    /// 将DateTime转换为13位时间戳
    pub fn datetime_to_timestamp(datetime: DateTime<Utc>) -> i64 {
        datetime.timestamp_millis()
    }

    /// 获取当前时间的13位时间戳
    pub fn get_current_timestamp() -> i64 {
        Utc::now().timestamp_millis()
    }

    /// 检查时间戳是否在指定范围内
    pub fn is_timestamp_in_range(timestamp: i64, range_seconds: u64) -> bool {
        let current = Self::get_current_timestamp();
        let range_millis = (range_seconds * 1000) as i64;
        (current - timestamp).abs() <= range_millis
    }

    /// 格式化时间戳为可读字符串
    pub fn format_timestamp(timestamp: i64) -> Result<String> {
        let datetime = Self::timestamp_to_datetime(timestamp)?;
        Ok(datetime.format("%Y-%m-%d %H:%M:%S%.3f UTC").to_string())
    }

    /// 计算两个时间戳的差值（毫秒）
    pub fn calculate_time_diff(timestamp1: i64, timestamp2: i64) -> i64 {
        (timestamp1 - timestamp2).abs()
    }
}

impl Default for shijian_chuo_guanliqii {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;

    #[test]
    fn test_shengcheng_shijian_chuo() {
        let timestamp = shijian_chuo_guanliqii::shengcheng_shijian_chuo();
        assert_eq!(timestamp.to_string().len(), 13);
        
        // 验证时间戳在合理范围内（当前时间前后1秒）
        let current = Utc::now().timestamp_millis();
        assert!((current - timestamp).abs() < 1000);
    }

    #[test]
    fn test_yanzheng_shijian_chuo() {
        let guanliqii = shijian_chuo_guanliqii::new();
        let current_timestamp = shijian_chuo_guanliqii::shengcheng_shijian_chuo();
        
        // 测试有效时间戳
        let result = guanliqii.yanzheng_shijian_chuo(current_timestamp);
        assert!(result.youxiao);
        assert!(result.cuowu_xinxi.is_none());
        
        // 测试重放攻击
        let result2 = guanliqii.yanzheng_shijian_chuo(current_timestamp);
        assert!(!result2.youxiao);
        assert!(result2.cuowu_xinxi.is_some());
    }

    #[test]
    fn test_guoqi_shijian_chuo() {
        let guanliqii = shijian_chuo_guanliqii::new_with_config(1, 100); // 1秒窗口
        
        // 创建过期的时间戳（2秒前）
        let old_timestamp = Utc::now().timestamp_millis() - 2000;
        let result = guanliqii.yanzheng_shijian_chuo(old_timestamp);
        
        assert!(!result.youxiao);
        assert!(result.cuowu_xinxi.is_some());
    }

    #[test]
    fn test_shijian_chuo_gongju() {
        let timestamp = shijian_chuo_gongju::get_current_timestamp();
        assert_eq!(timestamp.to_string().len(), 13);
        
        // 测试转换
        let datetime = shijian_chuo_gongju::timestamp_to_datetime(timestamp).unwrap();
        let converted_back = shijian_chuo_gongju::datetime_to_timestamp(datetime);
        assert_eq!(timestamp, converted_back);
        
        // 测试范围检查
        assert!(shijian_chuo_gongju::is_timestamp_in_range(timestamp, 10));
        assert!(!shijian_chuo_gongju::is_timestamp_in_range(timestamp - 20000, 10));
        
        // 测试格式化
        let formatted = shijian_chuo_gongju::format_timestamp(timestamp).unwrap();
        assert!(formatted.contains("UTC"));
    }

    #[test]
    fn test_qingli_gongneng() {
        let guanliqii = shijian_chuo_guanliqii::new();
        
        // 添加一些时间戳
        for i in 0..5 {
            let timestamp = shijian_chuo_guanliqii::shengcheng_shijian_chuo() + i;
            guanliqii.yanzheng_shijian_chuo(timestamp);
        }
        
        assert_eq!(guanliqii.get_cunchu_shuliang(), 5);
        
        // 清理所有
        guanliqii.qingli_suoyou_shijian_chuo();
        assert_eq!(guanliqii.get_cunchu_shuliang(), 0);
    }
}
