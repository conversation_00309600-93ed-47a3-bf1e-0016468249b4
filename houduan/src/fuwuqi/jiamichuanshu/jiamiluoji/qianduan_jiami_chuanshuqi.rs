#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit};
use aes_gcm::aead::{Aead, AeadCore, OsRng};
use anyhow::{Result, anyhow};
use sha2::{Sha256, Digest};

use super::jiami_shuju_jiegou::*;
use super::shijian_chuo_guanli::*;
use super::qianming_xiaoyan::*;
use crate::fuwuqi::jiamichuanshu::miyaochuanshu::qianduan_ecdh_miyaojiaohuan::qianduan_gongxiang_miyao;

/// 前端加密传输器
pub struct qianduan_jiami_chuanshuqi {
    /// AES-GCM加密器
    aes_jiamiqii: Aes256Gcm,
    /// 签名校验器
    qianming_xiaoyangqi: qianming_xiaoyangqi,
    /// 时间戳管理器
    shijian_chuo_guanliqii: shijian_chuo_guanliqii,
    /// 客户端ID
    kehuduan_id: String,
    /// 服务器会话ID
    fuwuqi_huihua_id: Option<String>,
    /// 密钥信息（用于签名）
    miyao_xinxi: String,
}

/// 前端加密结果
#[derive(Debug, Clone)]
pub struct qianduan_jiami_jieguo {
    /// 加密数据结构
    pub jiami_shuju: jiami_shuju,
    /// 加密成功标志
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 处理时间（毫秒）
    pub chuli_shijian: u64,
}

/// 前端解密结果
#[derive(Debug, Clone)]
pub struct qianduan_jiemi_jieguo {
    /// 解密数据结构
    pub jiemi_shuju: jiemi_shuju,
    /// 解密成功标志
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 处理时间（毫秒）
    pub chuli_shijian: u64,
}

impl qianduan_jiami_chuanshuqi {
    /// 从共享密钥创建前端加密传输器
    pub fn new(gongxiang_miyao: qianduan_gongxiang_miyao) -> Result<Self> {
        // 使用AES密钥创建加密器
        let aes_key = Key::<Aes256Gcm>::from_slice(&gongxiang_miyao.aes_miyao);
        let aes_jiamiqii = Aes256Gcm::new(aes_key);

        // 创建签名密钥（使用AES密钥的SHA256哈希）
        let mut hasher = Sha256::new();
        hasher.update(&gongxiang_miyao.aes_miyao);
        hasher.update(b"signature_key_derivation");
        let qianming_miyao = hasher.finalize().to_vec();

        // 创建签名校验器
        let qianming_xiaoyangqi = qianming_xiaoyangqi::new(qianming_miyao)?;

        // 创建时间戳管理器（前端使用更宽松的时间窗口）
        let shijian_chuo_guanliqii = shijian_chuo_guanliqii::new_with_config(600, 5000); // 10分钟窗口

        // 生成密钥信息字符串
        let miyao_xinxi = if let Some(ref huihua_id) = gongxiang_miyao.fuwuqi_huihua_id {
            format!("session_{}_aes256gcm", huihua_id)
        } else {
            format!("client_{}_aes256gcm", gongxiang_miyao.kehuduan_id)
        };

        Ok(Self {
            aes_jiamiqii,
            qianming_xiaoyangqi,
            shijian_chuo_guanliqii,
            kehuduan_id: gongxiang_miyao.kehuduan_id,
            fuwuqi_huihua_id: gongxiang_miyao.fuwuqi_huihua_id,
            miyao_xinxi,
        })
    }

    /// 加密字节数据
    pub fn jiami(&self, shuju: &[u8]) -> qianduan_jiami_jieguo {
        let kaishi_shijian = std::time::Instant::now();

        // 生成随机IV
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // 生成当前时间戳
        let shijian_chuo = shijian_chuo_guanliqii::shengcheng_shijian_chuo();

        match self.aes_jiamiqii.encrypt(&nonce, shuju) {
            Ok(miwen_with_tag) => {
                // AES-GCM返回的是密文+认证标签，需要分离
                if miwen_with_tag.len() < 16 {
                    return qianduan_jiami_jieguo {
                        jiami_shuju: jiami_shuju::new(Vec::new(), 0, Vec::new(), Vec::new(), Vec::new()),
                        chenggong: false,
                        cuowu_xinxi: Some("加密结果长度不足".to_string()),
                        chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
                    };
                }

                let miwen_len = miwen_with_tag.len() - 16;
                let miwen = miwen_with_tag[..miwen_len].to_vec();
                let tag = miwen_with_tag[miwen_len..].to_vec();

                // 生成数据签名
                match self.qianming_xiaoyangqi.wei_jiami_shuju_qianming(
                    &miwen,
                    shijian_chuo,
                    nonce.as_slice(),
                    &tag,
                    Some(&self.miyao_xinxi),
                ) {
                    Ok(qianming) => {
                        let jiami_shuju = jiami_shuju::new(
                            miwen,
                            shijian_chuo,
                            qianming,
                            nonce.to_vec(),
                            tag,
                        );

                        qianduan_jiami_jieguo {
                            jiami_shuju,
                            chenggong: true,
                            cuowu_xinxi: None,
                            chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
                        }
                    }
                    Err(e) => qianduan_jiami_jieguo {
                        jiami_shuju: jiami_shuju::new(Vec::new(), 0, Vec::new(), Vec::new(), Vec::new()),
                        chenggong: false,
                        cuowu_xinxi: Some(format!("生成签名失败: {}", e)),
                        chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
                    },
                }
            }
            Err(e) => qianduan_jiami_jieguo {
                jiami_shuju: jiami_shuju::new(Vec::new(), 0, Vec::new(), Vec::new(), Vec::new()),
                chenggong: false,
                cuowu_xinxi: Some(format!("AES加密失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        }
    }

    /// 解密数据
    pub fn jiemi(&self, jiami_shuju: &jiami_shuju) -> qianduan_jiemi_jieguo {
        let kaishi_shijian = std::time::Instant::now();

        // 验证数据结构
        if let Err(e) = jiami_shuju.validate() {
            return qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("数据结构验证失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            };
        }

        // 验证时间戳（前端使用更宽松的验证）
        let shijian_yanzheng = self.shijian_chuo_guanliqii.yanzheng_shijian_chuo(jiami_shuju.shijian_chuo);
        
        // 获取数据字节
        let miwen = match jiami_shuju.get_miwen_bytes() {
            Ok(data) => data,
            Err(e) => return qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("获取密文失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        };

        let iv = match jiami_shuju.get_iv_bytes() {
            Ok(data) => data,
            Err(e) => return qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("获取IV失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        };

        let tag = match jiami_shuju.get_tag_bytes() {
            Ok(data) => data,
            Err(e) => return qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("获取认证标签失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        };

        let qianming = match jiami_shuju.get_qianming_bytes() {
            Ok(data) => data,
            Err(e) => return qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("获取签名失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        };

        // 验证签名
        let qianming_yanzheng = self.qianming_xiaoyangqi.yanzheng_jiami_shuju_qianming(
            &miwen,
            jiami_shuju.shijian_chuo,
            &iv,
            &tag,
            &qianming,
            Some(&self.miyao_xinxi),
        );

        // 重构密文+标签用于解密
        let mut miwen_with_tag = miwen;
        miwen_with_tag.extend_from_slice(&tag);

        // 执行AES解密
        let nonce = Nonce::from_slice(&iv);
        match self.aes_jiamiqii.decrypt(nonce, miwen_with_tag.as_slice()) {
            Ok(yuanshi_shuju) => {
                let jiemi_shuju = jiemi_shuju::new(
                    yuanshi_shuju,
                    jiami_shuju.shijian_chuo,
                    qianming_yanzheng.chenggong,
                    shijian_yanzheng.youxiao,
                );

                qianduan_jiemi_jieguo {
                    jiemi_shuju,
                    chenggong: true,
                    cuowu_xinxi: None,
                    chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
                }
            }
            Err(e) => qianduan_jiemi_jieguo {
                jiemi_shuju: jiemi_shuju::new(Vec::new(), 0, false, false),
                chenggong: false,
                cuowu_xinxi: Some(format!("AES解密失败: {}", e)),
                chuli_shijian: kaishi_shijian.elapsed().as_millis() as u64,
            },
        }
    }

    /// 加密字符串
    pub fn jiami_zifuchuan(&self, zifuchuan: &str) -> qianduan_jiami_jieguo {
        self.jiami(zifuchuan.as_bytes())
    }

    /// 解密为字符串
    pub fn jiemi_wei_zifuchuan(&self, jiami_shuju: &jiami_shuju) -> Result<String> {
        let jieguo = self.jiemi(jiami_shuju);
        if !jieguo.chenggong {
            return Err(anyhow!("解密失败: {:?}", jieguo.cuowu_xinxi));
        }

        jieguo.jiemi_shuju.to_string()
    }

    /// 获取客户端ID
    pub fn get_kehuduan_id(&self) -> &str {
        &self.kehuduan_id
    }

    /// 获取服务器会话ID
    pub fn get_fuwuqi_huihua_id(&self) -> Option<&str> {
        self.fuwuqi_huihua_id.as_deref()
    }

    /// 获取密钥信息
    pub fn get_miyao_xinxi(&self) -> &str {
        &self.miyao_xinxi
    }

    /// 清理时间戳缓存
    pub fn qingli_shijian_chuo_huancun(&self) {
        self.shijian_chuo_guanliqii.qingli_suoyou_shijian_chuo();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::qianduan_ecdh_miyaojiaohuan::qianduan_ecdh_miyaojiaohuan;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::houduan_ecdh_miyaojiaohuan::houduan_ecdh_miyaojiaohuan;
    use crate::fuwuqi::jiamichuanshu::miyaochuanshu::qianduan_ecdh_miyaojiaohuan::qianduan_miyao_xiangying;

    #[test]
    fn test_qianduan_jiami_chuanshuqi_creation() {
        // 创建前端ECDH
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();
        let qianduan_gongyao = qianduan_ecdh.get_gongyao_shuju().unwrap();

        // 创建后端ECDH
        let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let houduan_gongyao = houduan_ecdh.get_gongyao_shuju().unwrap();

        // 后端计算共享密钥
        let _houduan_gongxiang = houduan_ecdh.jisuan_gongxiang_miyao_with_kehuduan_id(
            &qianduan_gongyao.gongyao,
            Some(&qianduan_gongyao.kehuduan_id)
        ).unwrap();

        // 前端处理响应
        let xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: houduan_gongyao.gongyao,
            fuwuqi_huihua_id: houduan_gongyao.huihua_id,
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };

        let qianduan_gongxiang = qianduan_ecdh.chuli_fuwuqi_xiangying(&xiangying).unwrap();

        // 创建前端加密传输器
        let jiami_chuanshuqi = qianduan_jiami_chuanshuqi::new(qianduan_gongxiang).unwrap();
        assert!(!jiami_chuanshuqi.get_kehuduan_id().is_empty());
        assert!(jiami_chuanshuqi.get_fuwuqi_huihua_id().is_some());
    }

    #[test]
    fn test_qianduan_jiami_jiemi_zifuchuan() {
        // 创建测试环境
        let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new().unwrap();
        let qianduan_gongyao = qianduan_ecdh.get_gongyao_shuju().unwrap();

        let houduan_ecdh = houduan_ecdh_miyaojiaohuan::new().unwrap();
        let houduan_gongyao = houduan_ecdh.get_gongyao_shuju().unwrap();

        let _houduan_gongxiang = houduan_ecdh.jisuan_gongxiang_miyao_with_kehuduan_id(
            &qianduan_gongyao.gongyao,
            Some(&qianduan_gongyao.kehuduan_id)
        ).unwrap();

        let xiangying = qianduan_miyao_xiangying {
            fuwuqi_gongyao: houduan_gongyao.gongyao,
            fuwuqi_huihua_id: houduan_gongyao.huihua_id,
            zhuangtai: "success".to_string(),
            cuowu_xinxi: None,
        };

        let qianduan_gongxiang = qianduan_ecdh.chuli_fuwuqi_xiangying(&xiangying).unwrap();
        let jiami_chuanshuqi = qianduan_jiami_chuanshuqi::new(qianduan_gongxiang).unwrap();

        let yuanshi_xiaoxi = "前端加密测试消息，包含中文！";

        // 加密
        let jiami_jieguo = jiami_chuanshuqi.jiami_zifuchuan(yuanshi_xiaoxi);
        assert!(jiami_jieguo.chenggong);
        assert!(jiami_jieguo.cuowu_xinxi.is_none());

        // 解密
        let jiemi_jieguo = jiami_chuanshuqi.jiemi(&jiami_jieguo.jiami_shuju);
        assert!(jiemi_jieguo.chenggong);
        assert!(jiemi_jieguo.cuowu_xinxi.is_none());

        // 验证解密结果
        let jiemi_xiaoxi = jiemi_jieguo.jiemi_shuju.to_string().unwrap();
        assert_eq!(yuanshi_xiaoxi, jiemi_xiaoxi);
        assert!(jiemi_jieguo.jiemi_shuju.qianming_youxiao);
        assert!(jiemi_jieguo.jiemi_shuju.shijian_youxiao);
    }
}
