#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::rizhixitong;

/// 路由系统信息日志
pub fn luyou_rizhi_xinxi(jiekou_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("路由系统模块中的{}接口：{}", jiekou_ming, xiaoxi);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 路由系统警告日志
pub fn luyou_rizhi_jinggao(jiekou_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("路由系统模块中的{}接口：{}", jiekou_ming, xiaoxi);
    rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
}

/// 路由系统错误日志
pub fn luyou_rizhi_cuowu(jiekou_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("路由系统模块中的{}接口：{}", jiekou_ming, xiaoxi);
    rizhixitong::rizhixitong_cuowu(&rizhi_xiaoxi);
}

/// 路由系统调试日志
pub fn luyou_rizhi_tiaoshi(jiekou_ming: &str, xiaoxi: &str) {
    let rizhi_xiaoxi = format!("路由系统模块中的{}接口：{}", jiekou_ming, xiaoxi);
    rizhixitong::rizhixitong_tiaoshi(&rizhi_xiaoxi);
}

/// 路由请求开始日志
pub fn luyou_qingqiu_kaishi(jiekou_ming: &str, qingqiu_lujing: &str, qingqiu_fangfa: &str) {
    let rizhi_xiaoxi = format!("接收到请求 - 接口：{}，路径：{}，方法：{}", jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 路由请求完成日志
pub fn luyou_qingqiu_wancheng(jiekou_ming: &str, chuli_shijian_ms: u64, zhuangtai_ma: u16) {
    let rizhi_xiaoxi = format!("请求处理完成 - 接口：{}，耗时：{}ms，状态码：{}", jiekou_ming, chuli_shijian_ms, zhuangtai_ma);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 路由请求失败日志
pub fn luyou_qingqiu_shibai(jiekou_ming: &str, cuowu_xinxi: &str, chuli_shijian_ms: u64) {
    let rizhi_xiaoxi = format!("请求处理失败 - 接口：{}，错误：{}，耗时：{}ms", jiekou_ming, cuowu_xinxi, chuli_shijian_ms);
    rizhixitong::rizhixitong_cuowu(&rizhi_xiaoxi);
}

/// 路由注册日志
pub fn luyou_zhuce_rizhi(jiekou_ming: &str, lujing: &str, fangfa: &str) {
    let rizhi_xiaoxi = format!("注册路由 - 接口：{}，路径：{}，方法：{}", jiekou_ming, lujing, fangfa);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 路由初始化日志
pub fn luyou_chushihua_rizhi(zhuce_shuliang: usize) {
    let rizhi_xiaoxi = format!("路由系统初始化完成，共注册{}个接口", zhuce_shuliang);
    rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
}

/// 路由参数验证失败日志
pub fn luyou_canshu_yanzheng_shibai(jiekou_ming: &str, canshu_ming: &str, cuowu_yuanyin: &str) {
    let rizhi_xiaoxi = format!("参数验证失败 - 接口：{}，参数：{}，原因：{}", jiekou_ming, canshu_ming, cuowu_yuanyin);
    rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
}

/// 路由权限检查日志
pub fn luyou_quanxian_jiancha(jiekou_ming: &str, yonghu_id: Option<&str>, jieguo: bool) {
    let yonghu_xinxi = yonghu_id.unwrap_or("匿名用户");
    let jieguo_wenzi = if jieguo { "通过" } else { "拒绝" };
    let rizhi_xiaoxi = format!("权限检查 - 接口：{}，用户：{}，结果：{}", jiekou_ming, yonghu_xinxi, jieguo_wenzi);

    if jieguo {
        rizhixitong::rizhixitong_xinxi(&rizhi_xiaoxi);
    } else {
        rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
    }
}

/// 路由限流日志
pub fn luyou_xianliu_rizhi(jiekou_ming: &str, ip_dizhi: &str, dangqian_qingqiu_shu: u32, xianzhi: u32) {
    let rizhi_xiaoxi = format!("限流检查 - 接口：{}，IP：{}，当前请求数：{}/{}", jiekou_ming, ip_dizhi, dangqian_qingqiu_shu, xianzhi);

    if dangqian_qingqiu_shu >= xianzhi {
        rizhixitong::rizhixitong_jinggao(&rizhi_xiaoxi);
    } else {
        rizhixitong::rizhixitong_tiaoshi(&rizhi_xiaoxi);
    }
}

/// 兼容性函数：根据消息内容自动判断日志级别
pub fn luyou_rizhi(jiekou_ming: &str, xiaoxi: &str) {
    let xiaoxi_xiaoxie = xiaoxi.to_lowercase();

    // 判断是否为错误信息
    if xiaoxi_xiaoxie.contains("失败") || xiaoxi_xiaoxie.contains("错误") ||
        xiaoxi_xiaoxie.contains("异常") || xiaoxi_xiaoxie.contains("error") ||
        xiaoxi_xiaoxie.contains("failed") || xiaoxi_xiaoxie.contains("exception") ||
        xiaoxi_xiaoxie.contains("超时") || xiaoxi_xiaoxie.contains("timeout") {
        luyou_rizhi_cuowu(jiekou_ming, xiaoxi);
    }
    // 判断是否为警告信息
    else if xiaoxi_xiaoxie.contains("警告") || xiaoxi_xiaoxie.contains("warn") ||
        xiaoxi_xiaoxie.contains("注意") || xiaoxi_xiaoxie.contains("限流") ||
        xiaoxi_xiaoxie.contains("权限") || xiaoxi_xiaoxie.contains("拒绝") {
        luyou_rizhi_jinggao(jiekou_ming, xiaoxi);
    }
    // 其他情况作为信息日志
    else {
        luyou_rizhi_xinxi(jiekou_ming, xiaoxi);
    }
}
