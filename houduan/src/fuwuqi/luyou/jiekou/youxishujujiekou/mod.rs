#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod guaiwushuju_jiekou;

use crate::fuwuqi::luyou::jiekou_dingyii;
use rocket::Route;

/// 获取游戏数据接口模块的所有路由
pub fn get_routes() -> Vec<Route> {
    let mut luyou = Vec::new();

    // 添加怪物数据接口路由
    luyou.extend(guaiwushuju_jiekou::guaiwushuju_jiekou::get_routes());

    luyou
}

/// 获取游戏数据接口模块的所有接口信息
pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        guaiwushuju_jiekou::guaiwushuju_jiekou::get_jiekou_xinxi()
    ]
}