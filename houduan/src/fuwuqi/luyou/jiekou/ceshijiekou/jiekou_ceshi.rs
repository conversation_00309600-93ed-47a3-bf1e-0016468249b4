#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{get, options};

/// 测试接口处理函数
#[get("/ceshijiekou")]
pub fn jiekou_ceshi_chuli() -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ceshi::get_miaoshu();
    let qingqiu_lujing = jiekou_ceshi::get_lujing();
    let qingqiu_fangfa = jiekou_ceshi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建测试数据
    let ceshi_shuju = serde_json::json!({
        "banben": "5.20",
        "fuwuqi_zhuangtai": "zhengchang",
        "shujuku_lianjie": "chenggong",
        "redis_lianjie": "chenggong",
        "dangqian_shijian": chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
        "jiekou_xinxi": {
            "lujing": qingqiu_lujing,
            "fangfa": qingqiu_fangfa,
            "miaoshu": jiekou_ming
        }
    });

    let xiangying = mingwen_xiangying::chenggong_with_shuju(
        "欢迎使用小落RO仙境传说资料站".to_string(),
        ceshi_shuju
    );

    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
    luyou_rizhi_xinxi(jiekou_ming, "测试接口调用成功");
    Json(xiangying)
}
#[options("/ceshijiekou")]
pub fn jiekou_ceshi_yujian() -> Status {
    Status::Ok
}
// 使用宏定义接口
dingyii_jiekou!(
    jiekou_ceshi,
    lujing: "/ceshijiekou",
    fangfa: "GET",
    miaoshu: "测试接口",
    jieshao: "这是一个用来测试路由系统是否阔以正常工作的接口",
    routes: [jiekou_ceshi_chuli, jiekou_ceshi_yujian]
);
