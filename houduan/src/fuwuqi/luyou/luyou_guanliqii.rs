#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::fuwuqi::luyou::jiekou::{ceshijiekou, youxishujujiekou};
use crate::fuwuqi::luyou::{luyou_chushihua_rizhi, luyou_zhuce_rizhi};
use rocket::Route;

/// 路由管理器
pub struct luyou_guanliqii {
    jiekou_xinxi: Vec<(String, String, String, String)>, // (路径, 方法, 描述, 介绍)
}

impl luyou_guanliqii {
    /// 创建新的路由管理器
    pub fn new() -> Self {
        let mut guanliqii = Self {
            jiekou_xinxi: Vec::new(),
        };

        // 自动注册所有接口信息
        guanliqii.zhuce_suoyou_jiekou();

        guanliqii
    }

    /// 注册所有接口信息
    fn zhuce_suoyou_jiekou(&mut self) {
        // 注册测试接口模块
        self.zhuce_jiekou_mokuai(ceshijiekou::get_jiekou_xinxi());

        // 注册游戏数据接口模块
        self.zhuce_jiekou_mokuai(youxishujujiekou::get_jiekou_xinxi());

        // 记录初始化完成日志
        luyou_chushihua_rizhi(self.jiekou_xinxi.len());
    }

    /// 注册接口模块的通用方法
    fn zhuce_jiekou_mokuai(&mut self, jiekou_xinxi_liebiao: Vec<(String, String, String, String)>) {
        for (lujing, fangfa, miaoshu, jieshao) in jiekou_xinxi_liebiao {
            self.jiekou_xinxi.push((lujing.clone(), fangfa.clone(), miaoshu.clone(), jieshao));
            // 记录路由注册日志
            luyou_zhuce_rizhi(&miaoshu, &lujing, &fangfa);
        }
    }

    /// 获取所有路由用于Rocket挂载
    pub fn get_suoyou_luyou() -> Vec<Route> {
        let mut luyou = Vec::new();

        // 收集所有接口模块的路由
        luyou.extend(ceshijiekou::get_routes());
        luyou.extend(youxishujujiekou::get_routes());

        luyou
    }

    /// 获取接口列表信息
    pub fn get_jiekou_liebiao(&self) -> &Vec<(String, String, String, String)> {
        &self.jiekou_xinxi
    }

    /// 获取接口数量
    pub fn get_jiekou_shuliang(&self) -> usize {
        self.jiekou_xinxi.len()
    }

    /// 根据路径查找接口信息
    pub fn chazhao_jiekou_by_lujing(&self, lujing: &str) -> Option<&(String, String, String, String)> {
        self.jiekou_xinxi.iter().find(|(l, _, _, _)| l == lujing)
    }

    /// 根据方法查找接口列表
    pub fn chazhao_jiekou_by_fangfa(&self, fangfa: &str) -> Vec<&(String, String, String, String)> {
        self.jiekou_xinxi.iter().filter(|(_, f, _, _)| f == fangfa).collect()
    }
}
