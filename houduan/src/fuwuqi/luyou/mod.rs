#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod luyou_rizhiguanli;
pub mod luyoujiegouti_chuli;
pub mod jiekou;
pub mod luyou_guanliqii;

use rocket::Route;

// 导出日志管理功能
pub use luyou_rizhiguanli::{
    luyou_chushihua_rizhi, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng,
    luyou_rizhi_xinxi, luyou_zhuce_rizhi,
};

/// 接口定义trait - 用于统一接口信息管理
pub trait jiekou_dingyii {
    /// 获取接口路径
    fn get_lujing() -> &'static str;
    /// 获取接口方法
    fn get_fangfa() -> &'static str;
    /// 获取接口描述
    fn get_miaoshu() -> &'static str;
    /// 获取接口介绍
    fn get_jieshao() -> &'static str;
    /// 获取路由列表
    fn get_routes() -> Vec<Route>;

    /// 获取接口信息元组
    fn get_jiekou_xinxi() -> (String, String, String, String) {
        (
            Self::get_lujing().to_string(),
            Self::get_fangfa().to_string(),
            Self::get_miaoshu().to_string(),
            Self::get_jieshao().to_string(),
        )
    }
}

/// 定义接口的宏 - 简化接口创建过程
#[macro_export]
macro_rules! dingyii_jiekou {
    (
        $struct_name:ident,
        lujing: $lujing:expr,
        fangfa: $fangfa:expr,
        miaoshu: $miaoshu:expr,
        jieshao: $jieshao:expr,
        routes: [$($route:ident),*]
    ) => {
        pub struct $struct_name;

        impl crate::fuwuqi::luyou::jiekou_dingyii for $struct_name {
            fn get_lujing() -> &'static str {
                $lujing
            }

            fn get_fangfa() -> &'static str {
                $fangfa
            }

            fn get_miaoshu() -> &'static str {
                $miaoshu
            }

            fn get_jieshao() -> &'static str {
                $jieshao
            }

            fn get_routes() -> Vec<rocket::Route> {
                rocket::routes![$($route),*]
            }
        }
    };
}



