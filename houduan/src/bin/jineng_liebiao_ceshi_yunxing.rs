use houduan::chushihua::peizhi::peizhixitong_guanli::peizhixitong_guanli;
use houduan::chushihua::shujukuxitong::mysqlshujuku::mysql_chushihua::mysql_chushihua;
use houduan::chushihua::shujukuxitong::redishujuku::redis_chushihua::redis_chushihua;
use houduan::chushihua::shujukuxitong::youxishujuchuli::jinengshuju::jineng_liebiao_ceshi::jineng_liebiao_ceshi;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("技能列表功能测试程序启动");

    // 创建配置管理器并初始化配置
    let peizhi_guanli = peizhixitong_guanli::peizhixitong_new();
    peizhi_guanli.peizhixitong_chushihua()?;
    println!("✓ 配置初始化成功");

    // 初始化MySQL连接
    let mysql_guanli = mysql_chushihua::chushihua(&peizhi_guanli).await?;
    println!("✓ MySQL连接初始化成功");

    // 尝试初始化Redis连接
    let redis_guanli = match redis_chushihua::chushihua(&peizhi_guanli) {
        Ok(redis) => {
            println!("✓ Redis连接初始化成功");
            Some(redis)
        }
        Err(e) => {
            println!("⚠ Redis连接初始化失败: {}，将使用无缓存模式", e);
            None
        }
    };

    // 运行完整的技能列表功能测试
    match jineng_liebiao_ceshi::wanzheng_jineng_liebiao_ceshi(mysql_guanli, redis_guanli).await {
        Ok(_) => {
            println!("\n🎉 所有测试完成！");
        }
        Err(e) => {
            println!("\n❌ 测试过程中发生错误: {}", e);
            return Err(e);
        }
    }

    Ok(())
}
