#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::path::PathBuf;
use anyhow::Result;
use tokio::fs;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum rizhixitong_leixing {
    cuowu,
    zhengchang,
}

impl rizhixitong_leixing {
    pub fn get_mulu_mingcheng(&self) -> &'static str {
        match self {
            rizhixitong_leixing::cuowu => "cuowu",
            rizhixitong_leixing::zhengchang => "zhengchang",
        }
    }
}

pub struct rizhixitong_fenlei {
    genmulu: PathBuf,
}

impl rizhixitong_fenlei {
    pub fn new(genmulu: PathBuf) -> Self {
        rizhixitong_fenlei { genmulu }
    }
    
    pub async fn chuangjian_mulu_jiegou(&self) -> Result<()> {
        fs::create_dir_all(&self.genmulu).await?;
        
        let cuowu_mulu = self.genmulu.join("cuowu");
        fs::create_dir_all(&cuowu_mulu).await?;
        
        let zhengchang_mulu = self.genmulu.join("zhengchang");
        fs::create_dir_all(&zhengchang_mulu).await?;
        
        Ok(())
    }
    
    pub fn get_rizhiwenjian_lujing(&self, leixing: rizhixitong_leixing, riqi: &str) -> PathBuf {
        let zimulu = self.genmulu.join(leixing.get_mulu_mingcheng());
        zimulu.join(format!("{}.log", riqi))
    }
    
    pub fn get_cuowu_mulu(&self) -> PathBuf {
        self.genmulu.join("cuowu")
    }
    
    pub fn get_zhengchang_mulu(&self) -> PathBuf {
        self.genmulu.join("zhengchang")
    }
    
    pub fn get_genmulu(&self) -> &PathBuf {
        &self.genmulu
    }
}
