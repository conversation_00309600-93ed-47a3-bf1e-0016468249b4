#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::path::PathBuf;
use std::sync::Arc;
use anyhow::Result;
use tokio::sync::OnceCell;

use crate::rizhixitong::rizhixitong_xieruqi::{rizhixitong_xieruqi, rizhixitong_shuchu_moshi};

static rizhixitong_quanju_rizhiqi: OnceCell<Arc<rizhixitong_xieruqi>> = OnceCell::const_new();

pub async fn rizhixitong_chushihua_rizhixitong() -> Result<()> {
    rizhixitong_chushihua_rizhixitong_with_path("./rizhi").await
}

pub async fn rizhixitong_chushihua_rizhixitong_with_path(lujing: &str) -> Result<()> {
    let rizhimulu = PathBuf::from(lujing);
    
    tokio::fs::create_dir_all(&rizhimulu).await?;
    
    let rizhiqi = rizhixitong_xieruqi::new(rizhimulu).await?;
    let rizhiqi_arc = Arc::new(rizhiqi);
    
    rizhixitong_quanju_rizhiqi.set(rizhiqi_arc.clone())
        .map_err(|_| anyhow::anyhow!("日志系统已经初始化过了"))?;
    
    Ok(())
}

pub fn rizhixitong_get_quanju_rizhiqi() -> Option<Arc<rizhixitong_xieruqi>> {
    rizhixitong_quanju_rizhiqi.get().cloned()
}

pub fn rizhixitong_xinxi(neirong: &str) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.xinxi(neirong);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_jinggao(neirong: &str) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.jinggao(neirong);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_cuowu(neirong: &str) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.cuowu(neirong);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_tiaoshi(neirong: &str) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.tiaoshi(neirong);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

// 带输出模式的日志函数
pub fn rizhixitong_xinxi_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.xinxi_with_moshi(neirong, shuchu_moshi);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_jinggao_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.jinggao_with_moshi(neirong, shuchu_moshi);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_cuowu_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.cuowu_with_moshi(neirong, shuchu_moshi);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

pub fn rizhixitong_tiaoshi_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
    if let Some(rizhiqi) = rizhixitong_get_quanju_rizhiqi() {
        rizhiqi.tiaoshi_with_moshi(neirong, shuchu_moshi);
    } else {
        eprintln!("日志系统未初始化: {}", neirong);
    }
}

#[macro_export]
macro_rules! rizhixitong_rizhi_xinxi {
    ($($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_xinxi(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_jinggao {
    ($($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_jinggao(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_cuowu {
    ($($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_cuowu(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_tiaoshi {
    ($($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_tiaoshi(&format!($($arg)*))
    };
}

// 带输出模式的宏定义
#[macro_export]
macro_rules! rizhixitong_rizhi_xinxi_with_moshi {
    ($shuchu_moshi:expr, $($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_xinxi_with_moshi(&format!($($arg)*), $shuchu_moshi)
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_jinggao_with_moshi {
    ($shuchu_moshi:expr, $($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_jinggao_with_moshi(&format!($($arg)*), $shuchu_moshi)
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_cuowu_with_moshi {
    ($shuchu_moshi:expr, $($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_cuowu_with_moshi(&format!($($arg)*), $shuchu_moshi)
    };
}

#[macro_export]
macro_rules! rizhixitong_rizhi_tiaoshi_with_moshi {
    ($shuchu_moshi:expr, $($arg:tt)*) => {
        $crate::rizhixitong::rizhixitong_chushihua::rizhixitong_tiaoshi_with_moshi(&format!($($arg)*), $shuchu_moshi)
    };
}
