#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::path::PathBuf;
use std::sync::Arc;
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncWriteExt, BufWriter};
use tokio::sync::{mpsc, Mutex};
use chrono::{DateTime, Local};
use anyhow::Result;

#[derive(Debug, Clone)]
pub enum rizhixitong_shuchu_moshi {
    jinmo,     // 静默模式，只写文件
    xiangxi,   // 详细模式，写文件+控制台输出
}

#[derive(Debug, <PERSON>lone)]
pub struct rizhixitong_xiaoxi {
    pub neirong: String,
    pub shijian: DateTime<Local>,
    pub dengji: rizhixitong_dengji,
    pub shuchu_moshi: rizhixitong_shuchu_moshi,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum rizhixitong_dengji {
    xinxi,
    jinggao,
    cuowu,
    tiaoshi,
}

impl rizhixitong_dengji {
    pub fn to_string(&self) -> &'static str {
        match self {
            rizhixitong_dengji::xinxi => "INFO",
            rizhixitong_dengji::jinggao => "WARN", 
            rizhixitong_dengji::cuowu => "ERROR",
            rizhixitong_dengji::tiaoshi => "DEBUG",
        }
    }
}

pub struct rizhixitong_xieruqi {
    fasongqi: mpsc::UnboundedSender<rizhixitong_xiaoxi>,
    _jieshouqi_handle: tokio::task::JoinHandle<()>,
}

impl rizhixitong_xieruqi {
    pub async fn new(rizhimulu: PathBuf) -> Result<Self> {
        let (fasongqi, mut jieshouqi) = mpsc::unbounded_channel::<rizhixitong_xiaoxi>();
        
        let rizhimulu_clone = rizhimulu.clone();
        let jieshouqi_handle = tokio::spawn(async move {
            let mut dangqian_wenjian: Option<Arc<Mutex<BufWriter<File>>>> = None;
            let mut dangqian_riqi = String::new();
            let mut dangqian_leixing: Option<bool> = None; // true为错误，false为正常
            
            while let Some(xiaoxi) = jieshouqi.recv().await {
                let riqi_str = xiaoxi.shijian.format("%Y-%m-%d").to_string();
                let shi_cuowu = matches!(xiaoxi.dengji, rizhixitong_dengji::cuowu);
                
                // 如果日期变了或者日志类型变了，需要切换文件
                if riqi_str != dangqian_riqi || dangqian_leixing != Some(shi_cuowu) {
                    dangqian_riqi = riqi_str.clone();
                    dangqian_leixing = Some(shi_cuowu);
                    
                    let wenjianming = if shi_cuowu {
                        let cuowu_mulu = rizhimulu_clone.join("cuowu");
                        if let Err(e) = tokio::fs::create_dir_all(&cuowu_mulu).await {
                            eprintln!("创建错误日志目录失败: {}", e);
                            continue;
                        }
                        cuowu_mulu.join(format!("{}.log", riqi_str))
                    } else {
                        let zhengchang_mulu = rizhimulu_clone.join("zhengchang");
                        if let Err(e) = tokio::fs::create_dir_all(&zhengchang_mulu).await {
                            eprintln!("创建正常日志目录失败: {}", e);
                            continue;
                        }
                        zhengchang_mulu.join(format!("{}.log", riqi_str))
                    };
                    
                    match OpenOptions::new()
                        .create(true)
                        .append(true)
                        .open(&wenjianming)
                        .await
                    {
                        Ok(file) => {
                            dangqian_wenjian = Some(Arc::new(Mutex::new(BufWriter::new(file))));
                        }
                        Err(e) => {
                            eprintln!("打开日志文件失败 {}: {}", wenjianming.display(), e);
                            continue;
                        }
                    }
                }
                
                if let Some(ref wenjian) = dangqian_wenjian {
                    let rizhihang = format!(
                        "[{}] [{}] {}\n",
                        xiaoxi.shijian.format("%Y-%m-%d %H:%M:%S%.3f"),
                        xiaoxi.dengji.to_string(),
                        xiaoxi.neirong
                    );

                    // 根据输出模式决定是否打印到控制台
                    match xiaoxi.shuchu_moshi {
                        rizhixitong_shuchu_moshi::xiangxi => {
                            print!("{}", rizhihang);
                        }
                        rizhixitong_shuchu_moshi::jinmo => {
                            // 静默模式，不打印到控制台
                        }
                    }

                    let mut writer = wenjian.lock().await;
                    if let Err(e) = writer.write_all(rizhihang.as_bytes()).await {
                        eprintln!("写入日志失败: {}", e);
                    } else if let Err(e) = writer.flush().await {
                        eprintln!("刷新日志缓冲区失败: {}", e);
                    }
                }
            }
        });
        
        Ok(rizhixitong_xieruqi {
            fasongqi,
            _jieshouqi_handle: jieshouqi_handle,
        })
    }
    
    pub fn xie_rizhi(&self, neirong: String, dengji: rizhixitong_dengji) {
        self.xie_rizhi_with_moshi(neirong, dengji, rizhixitong_shuchu_moshi::jinmo);
    }

    pub fn xie_rizhi_with_moshi(&self, neirong: String, dengji: rizhixitong_dengji, shuchu_moshi: rizhixitong_shuchu_moshi) {
        let xiaoxi = rizhixitong_xiaoxi {
            neirong,
            shijian: Local::now(),
            dengji,
            shuchu_moshi,
        };

        if self.fasongqi.send(xiaoxi).is_err() {
            eprintln!("发送日志消息失败：通道已关闭");
        }
    }
    
    pub fn xinxi(&self, neirong: &str) {
        self.xie_rizhi(neirong.to_string(), rizhixitong_dengji::xinxi);
    }

    pub fn jinggao(&self, neirong: &str) {
        self.xie_rizhi(neirong.to_string(), rizhixitong_dengji::jinggao);
    }

    pub fn cuowu(&self, neirong: &str) {
        self.xie_rizhi(neirong.to_string(), rizhixitong_dengji::cuowu);
    }

    pub fn tiaoshi(&self, neirong: &str) {
        self.xie_rizhi(neirong.to_string(), rizhixitong_dengji::tiaoshi);
    }

    // 带输出模式的日志方法
    pub fn xinxi_with_moshi(&self, neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
        self.xie_rizhi_with_moshi(neirong.to_string(), rizhixitong_dengji::xinxi, shuchu_moshi);
    }

    pub fn jinggao_with_moshi(&self, neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
        self.xie_rizhi_with_moshi(neirong.to_string(), rizhixitong_dengji::jinggao, shuchu_moshi);
    }

    pub fn cuowu_with_moshi(&self, neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
        self.xie_rizhi_with_moshi(neirong.to_string(), rizhixitong_dengji::cuowu, shuchu_moshi);
    }

    pub fn tiaoshi_with_moshi(&self, neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi) {
        self.xie_rizhi_with_moshi(neirong.to_string(), rizhixitong_dengji::tiaoshi, shuchu_moshi);
    }
}
