#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::fs::{File, OpenOptions};
use tokio::io::BufWriter;
use tokio::sync::Mutex;
use chrono::{DateTime, Local};
use anyhow::Result;

use crate::rizhixitong::rizhixitong_fenlei::{rizhixitong_leixing, rizhixitong_fenlei};

type wenjian_huancun_leixing = Arc<Mutex<HashMap<String, Arc<Mutex<BufWriter<File>>>>>>;

pub struct rizhixitong_lunhuan {
    fenlei: rizhixitong_fenlei,
    dangqian_wenjian: wenjian_huancun_leixing,
}

impl rizhixitong_lunhuan {
    pub fn new(genmulu: PathBuf) -> Self {
        let fenlei = rizhixitong_fenlei::new(genmulu);
        let dangqian_wenjian = Arc::new(Mutex::new(HashMap::new()));
        
        rizhixitong_lunhuan {
            fenlei,
            dangqian_wenjian,
        }
    }
    
    pub async fn chuangjian_mulu(&self) -> Result<()> {
        self.fenlei.chuangjian_mulu_jiegou().await
    }
    
    pub async fn get_rizhiwenjian(
        &self,
        leixing: rizhixitong_leixing,
        shijian: DateTime<Local>,
    ) -> Result<Arc<Mutex<BufWriter<File>>>> {
        let riqi_str = shijian.format("%Y-%m-%d").to_string();
        let wenjian_key = format!("{}_{}", leixing.get_mulu_mingcheng(), riqi_str);
        
        let mut wenjian_map = self.dangqian_wenjian.lock().await;
        
        if let Some(wenjian) = wenjian_map.get(&wenjian_key) {
            return Ok(wenjian.clone());
        }
        
        let wenjian_lujing = self.fenlei.get_rizhiwenjian_lujing(leixing, &riqi_str);
        
        if let Some(parent) = wenjian_lujing.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        
        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&wenjian_lujing)
            .await?;
            
        let buf_writer = BufWriter::new(file);
        let wenjian_arc = Arc::new(Mutex::new(buf_writer));
        
        wenjian_map.insert(wenjian_key, wenjian_arc.clone());
        
        Ok(wenjian_arc)
    }
    
    pub async fn qingli_guoqi_wenjian(&self) {
        let dangqian_riqi = Local::now().format("%Y-%m-%d").to_string();
        let mut wenjian_map = self.dangqian_wenjian.lock().await;
        
        wenjian_map.retain(|key, _| {
            key.ends_with(&dangqian_riqi)
        });
    }
    
    pub fn get_fenlei(&self) -> &rizhixitong_fenlei {
        &self.fenlei
    }
}
