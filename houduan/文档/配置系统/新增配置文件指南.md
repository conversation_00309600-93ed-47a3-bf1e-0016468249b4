# 新增配置文件指南

本文档提供了向配置系统中添加新配置项的详细步骤。

## 概述

当前系统使用一个总配置结构体 `peizhixitong_zongpeizhi` 来管理所有配置。增加新的配置项需要修改定义该结构体的文件，并确保新的配置项具有默认值。

## 操作步骤

假设我们需要添加一个新的配置项，例如一个用于管理 `API` 密钥的配置。

### 步骤 1: 定义新的配置结构体

首先，在 `houduan/src/chushihua/peizhi/zongpeizhi_peizhi.rs` 文件中，定义一个新的结构体来存放 `API` 相关的配置。

```rust
// houduan/src/chushihua/peizhi/zongpeizhi_peizhi.rs

// ... 其他 use 语句

use serde::{Deserialize, Serialize};

// 新增一个 API 配置结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct peizhixitong_api_peizhi {
    pub api_key: String,
    pub api_secret: String,
}

// 为新的 API 配置结构体实现 Default trait，提供默认值
impl Default for peizhixitong_api_peizhi {
    fn default() -> Self {
        Self {
            api_key: "default_api_key".to_string(),
            api_secret: "default_api_secret".to_string(),
        }
    }
}

// ... 其他结构体定义
```

### 步骤 2: 将新结构体添加到总配置中

接下来，将刚刚创建的 `peizhixitong_api_peizhi` 结构体作为新字段添加到 `peizhixitong_zongpeizhi` 总配置结构体中。

```rust
// houduan/src/chushihua/peizhi/zongpeizhi_peizhi.rs

// ...

/// 总配置结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct peizhixitong_zongpeizhi {
    pub yunxingduankou: u16,
    pub shujuku: peizhixitong_shujuku_peizhi,
    pub api: peizhixitong_api_peizhi, // 新增的字段
}

// ...
```

### 步骤 3: 更新总配置的默认值

最后，修改 `peizhixitong_zongpeizhi` 的 `Default` 实现，以包含新增的 `api` 字段。

```rust
// houduan/src/chushihua/peizhi/zongpeizhi_peizhi.rs

// ...

impl Default for peizhixitong_zongpeizhi {
    fn default() -> Self {
        Self {
            yunxingduankou: 8098,
            shujuku: peizhixitong_shujuku_peizhi::default(),
            api: peizhixitong_api_peizhi::default(), // 添加新字段的默认值
        }
    }
}

// ...
```

## 完成

完成以上步骤后，新的配置项就已经成功集成到配置系统中了。当系统启动时，如果 `zongpeizhi.yml` 文件中缺少 `api` 配置，系统会自动使用您提供的默认值创建它。

您可以在 `zongpeizhi.yml` 文件中手动修改这些值：

```yaml
yunxingduankou: 8098
shujuku:
  shujukuip: 127.0.0.1
  yonghu: root
  mima: '111222'
api:
  api_key: your_actual_api_key
  api_secret: your_actual_api_secret