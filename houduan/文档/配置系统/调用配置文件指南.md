# 调用配置文件指南

本文档详细说明了如何在应用程序中获取和使用配置项。

## 概述

配置系统的核心是 `peizhixitong_guanli` 管理器。所有对配置的访问都应该通过它来进行，以确保线程安全和数据一致性。

在应用程序启动时，通常会创建一个全局的、共享的 `peizhixitong_guanli` 实例。

## 获取配置的推荐方法

获取配置信息的唯一推荐方法是调用 `peizhixitong_guanli` 实例的 `peizhixitong_get_peizhi()` 方法。

### 示例

假设您在 `main.rs` 或其他业务逻辑模块中需要访问数据库配置。

```rust
// 假设 peizhi_guanli 是一个已经初始化好的 peizhixitong_guanli 实例
// let peizhi_guanli = peizhixitong_guanli::peizhixitong_new();
// peizhi_guanli.peizhixitong_chushihua().unwrap();

// 1. 调用 peizhixitong_get_peizhi() 获取配置快照
match peizhi_guanli.peizhixitong_get_peizhi() {
    Ok(dangqian_peizhi) => {
        // 2. 从返回的 peizhixitong_zongpeizhi 结构体中访问你需要的字段
        let db_ip = &dangqian_peizhi.shujuku.shujukuip;
        let db_user = &dangqian_peizhi.shujuku.yonghu;
        
        println!("数据库IP: {}", db_ip);
        println!("数据库用户: {}", db_user);
        
        // 你可以在这里使用获取到的配置进行数据库连接等操作
    }
    Err(e) => {
        // 处理获取配置失败的错误
        eprintln!("无法获取配置: {}", e);
    }
}
```

### 关键点

- **返回的是克隆 (Clone)**: `peizhixitong_get_peizhi()` 方法返回的是当前配置的一个完整克隆（快照），而不是一个引用。这意味着你得到的是一个在调用时刻的配置副本。这样做的好处是：
  - **线程安全**: 你可以在自己的线程中自由使用这个副本，而不用担心其他线程会修改它。
  - **避免死锁**: 由于不持有锁，因此不会因为长时间持有配置读取权而阻塞其他需要写入配置的线程。

- **性能考量**: 因为每次调用都会克隆整个配置结构体，所以不建议在循环或性能敏感的代码中频繁调用。最佳实践是在函数或任务开始时获取一次配置，然后在接下来的执行过程中使用这个副本。

## 更新配置

如果需要修改配置，应该使用 `peizhixitong_gengxin_peizhi()` 方法，而不是直接修改你获取到的配置副本。

```rust
// 假设 peizhi_guanli 是一个已经初始化好的 peizhixitong_guanli 实例

// 1. 获取当前配置
if let Ok(mut dangqian_peizhi) = peizhi_guanli.peizhixitong_get_peizhi() {
    // 2. 修改需要更新的字段
    dangqian_peizhi.yunxingduankou = 8888;
    
    // 3. 调用更新方法
    if let Err(e) = peizhi_guanli.peizhixitong_gengxin_peizhi(dangqian_peizhi) {
        eprintln!("更新配置失败: {}", e);
    }
}
```
该方法会首先将新配置写入文件，然后再更新内存中的配置，确保了操作的原子性和持久性。