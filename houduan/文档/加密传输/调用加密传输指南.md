# 调用加密传输指南

本文档详细说明了如何在应用程序中获取和使用加密传输功能。

## 概述

加密传输系统的核心是 `miyaojiaohuan_guanliqii`（密钥交换管理器）和 `houduan_jiamichuanshu`（后端加密传输器）。所有加密和解密操作都应通过这些组件进行，以确保安全性和会话一致性。

在应用程序启动时，通常会创建一个全局的、共享的 `miyaojiaohuan_guanliqii` 实例来管理所有客户端的密钥交换会话。

## 获取和使用加密传输器

与配置系统不同，加密传输器不是直接获取的，而是基于一个有效的会话和共享密钥动态创建的。每次需要加密或解密时，都应从会话管理器中重现密钥，然后创建加密器实例。

### 示例

假设您在一个 `axum` 路由处理函数中需要解密一个传入的加密消息。

```rust
// 假设 miyao_guanliqi 是一个已经初始化好的 miyaojiaohuan_guanliqii 全局实例
// let miyao_guanliqi = Arc::new(miyaojiaohuan_guanliqii::new());

// 1. 从请求中获取加密消息和会话ID
// let jiami_xiaoxi: jiami_xiaoxi = ...; // 从请求体中反序列化
// let huihua_id = &jiami_xiaoxi.huihua_id;
// let kehuduan_gongyao = ...; // 客户端公钥通常在密钥交换后保存或从客户端再次获取

// 2. 使用会话ID和客户端公钥重现共享密钥
match miyao_guanliqi.chongxian_jiaohuan_miyao(huihua_id, &kehuduan_gongyao) {
    Ok(gongxiang_miyao) => {
        // 3. 基于重现的共享密钥创建加密传输器实例
        let jiami_chuanshuqi = houduan_jiamichuanshu::new(gongxiang_miyao);

        // 4. 使用传输器解密数据
        match jiami_chuanshuqi.jiemi(&jiami_xiaoxi) {
            Ok(jiemi_shuju) => {
                // 处理解密后的数据
                println!("解密成功: {:?}", jiemi_shuju);
            }
            Err(e) => {
                eprintln!("解密失败: {}", e);
            }
        }
    }
    Err(e) => {
        // 处理密钥重现失败的错误（例如，会话过期或ID无效）
        eprintln!("无法重现密钥: {}", e);
    }
}```

### 关键点

- **动态创建**: `houduan_jiamichuanshu` 实例是轻量级的，并且是为单次操作或单个请求生命周期设计的。它不应该被长期存储或在多个线程间共享。
- **密钥重现**: `chongxian_jiaohuan_miyao` 是核心安全步骤。它确保只有拥有有效会话和正确客户端公钥的请求才能访问共享密钥。这可以防止会话劫持。
- **线程安全**: `miyaojiaohuan_guanliqii` 内部使用 `Arc<Mutex<...>>` 来保护会话数据，因此可以安全地在多线程环境（如 `axum` 或 `actix-web`）中共享。
- **性能考量**: 密钥重现涉及一次 `HashMap` 查找和一次 `ECDH` 计算，性能非常高。但是，为了避免在每次需要时都重新进行完整的密钥交换，客户端应在本地缓存其密钥对和会话ID。

## 加密数据

加密数据的流程与解密类似。

```rust
// ... 获取共享密钥和创建加密传输器的步骤同上 ...

// 5. 加密字符串数据
match jiami_chuanshuqi.jiami_zifuchuan("这是需要发送的敏感数据") {
    Ok(jiami_xiaoxi) => {
        // 6. 将加密消息发送回客户端
        // ...
    }
    Err(e) => {
        eprintln!("加密失败: {}", e);
    }
}