# 新增加密功能指南

本文档提供了向加密传输系统中添加新功能（例如，支持新的加密算法或扩展现有功能）的详细步骤。

## 概述

加密传输系统是模块化的，主要分为 `miyaochuanshu`（密钥传输）和 `jiamiluoji`（加密逻辑）两个部分。在添加新功能时，请务必遵循现有代码的结构和安全实践。

## 操作步骤

假设我们需要添加一个新的功能：**为每个加密消息添加数字签名**，以确保消息的完整性和不可否认性。

### 步骤 1: 在 `jiamiluoji` 模块中定义签名功能

首先，在 `houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/` 目录下创建一个新文件 `qianming_xiaoyan.rs`，用于处理签名和验签逻辑。

```rust
// houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/qianming_xiaoyan.rs

use p256::ecdsa::{SigningKey, Signature, VerifyingKey};
use sha2::{Sha256, Digest};

// 创建签名
pub fn chuangjian_qianming(siyao: &SigningKey, shuju: &[u8]) -> Signature {
    let mut hasher = Sha256::new();
    hasher.update(shuju);
    let digest = hasher.finalize();
    siyao.sign(&digest)
}

// 验证签名
pub fn yanzheng_qianming(gongyao: &VerifyingKey, shuju: &[u8], qianming: &Signature) -> bool {
    let mut hasher = Sha256::new();
    hasher.update(shuju);
    let digest = hasher.finalize();
    gongyao.verify(&digest, qianming).is_ok()
}
```

然后，在 `houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/mod.rs` 中导出新模块。

```rust
// houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/mod.rs
pub mod qianming_xiaoyan;
pub use qianming_xiaoyan::*;
```

### 步骤 2: 更新加密消息结构体

接下来，修改 `jiami_shuju_jiegou.rs` 中的 `jiami_xiaoxi` 结构体，添加一个用于存放签名的字段。

```rust
// houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/jiami_shuju_jiegou.rs

// ...
pub struct jiami_xiaoxi {
    pub jiami_shuju: String,    // Base64编码的加密数据
    pub suijishu: String,       // Base64编码的nonce
    pub huihua_id: String,      // 会话ID
    pub xiaoxi_id: String,      // 消息ID(UUID)
    pub shijian_chuo: u64,      // Unix时间戳
    pub qianming: String,       // 新增：Base64编码的签名
}
```

### 步骤 3: 集成签名逻辑到加密和解密流程

最后，修改 `houduan_jiami_chuanshuqi.rs`，在加密时创建签名，在解密时验证签名。

#### 加密流程修改

```rust
// houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/houduan_jiami_chuanshuqi.rs

// ...
impl houduan_jiamichuanshu {
    pub fn jiami_zifuchuan(&self, mingwen: &str) -> Result<jiami_xiaoxi> {
        // ... (现有加密逻辑)

        // 创建待签名的数据（例如，加密数据+nonce+时间戳）
        let dai_qianming_shuju = format!("{}{}{}", &jiami_shuju_base64, &suijishu_base64, &shijian_chuo);

        // 使用ECDH私钥进行签名
        let qianming = chuangjian_qianming(&self.gongxiang_miyao.get_siyao(), dai_qianming_shuju.as_bytes());
        let qianming_base64 = base64::encode(qianming.to_bytes());

        Ok(jiami_xiaoxi {
            // ... (其他字段)
            qianming: qianming_base64,
        })
    }
}
```

#### 解密流程修改

```rust
// houduan/src/fuwuqi/jiamichuanshu/jiamiluoji/houduan_jiami_chuanshuqi.rs

// ...
impl houduan_jiamichuanshu {
    pub fn jiemi(&self, xiaoxi: &jiami_xiaoxi) -> Result<Vec<u8>> {
        // 1. 验证签名
        let dai_yanzheng_shuju = format!("{}{}{}", &xiaoxi.jiami_shuju, &xiaoxi.suijishu, &xiaoxi.shijian_chuo);
        let qianming = Signature::from_bytes(&base64::decode(&xiaoxi.qianming)?)?;

        if !yanzheng_qianming(&self.gongxiang_miyao.get_duifang_gongyao(), dai_yanzheng_shuju.as_bytes(), &qianming) {
            return Err(anyhow::anyhow!("签名验证失败"));
        }

        // 2. 如果签名有效，则继续执行现有解密逻辑
        // ...
    }
}
```

## 完成

完成以上步骤后，新的数字签名功能就已经成功集成到加密传输系统中了。这不仅增强了消息的安全性，也展示了如何在不破坏现有架构的情况下扩展系统功能。