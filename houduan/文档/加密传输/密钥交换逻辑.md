# 密钥交换逻辑架构文档

## 概述

本文档详细描述了基于ECDH（椭圆曲线迪菲-赫尔曼）算法的密钥交换系统架构。该系统支持前后端安全密钥交换，具备会话管理、密钥重现、自动清理等功能，为加密通信提供安全可靠的密钥协商机制。

## 系统架构图

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐
│   前端客户端     │ ◄──────────────► │   后端服务器     │
│                │                 │                │
│ qianduan_ecdh   │                 │ miyaojiaohuan   │
│ _miyaojiaohuan  │                 │ _guanliqii      │
│                │                 │                │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 私钥/公钥   │ │                 │ │ 会话管理器   │ │
│ │ 客户端ID    │ │                 │ │ HashMap     │ │
│ └─────────────┘ │                 │ └─────────────┘ │
│                │                 │                │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 加密传输器   │ │                 │ │ ECDH实例    │ │
│ │ AES-256-GCM │ │                 │ │ 密钥存储    │ │
│ └─────────────┘ │                 │ └─────────────┘ │
└─────────────────┘                 └─────────────────┘
```

## 核心模块详解

### 1. 后端ECDH密钥交换器 (`houduan_ecdh_miyaojiaohuan`)

**用途**: 后端密钥交换的核心组件，负责生成密钥对、计算共享密钥、验证公钥格式。

**性能特点**:
- 使用P-256椭圆曲线，安全性高
- 密钥生成使用硬件随机数生成器(OsRng)
- 支持密钥重现，避免重复计算
- 内存占用约200字节/实例

**主要结构体**:
```rust
pub struct houduan_ecdh_miyaojiaohuan {
    siyao: SecretKey,                    // 32字节私钥
    gongyao: PublicKey,                  // 65字节公钥
    huihua_id: String,                   // UUID会话标识
    chuangjian_shijian: DateTime<Utc>,   // 创建时间戳
    cunchu_gongyao_shuju: Option<houduan_gongyao_shuju>, // 缓存的公钥数据
}
```

**核心方法**:
- `new()`: 创建新实例，生成随机密钥对
- `get_gongyao_shuju()`: 获取带时间戳的公钥数据
- `jisuan_gongxiang_miyao()`: 计算共享密钥
- `yanzheng_gongyao_geshi()`: 验证公钥格式和时间戳
- `cunchu_gongyao_shuju()`: 存储公钥数据用于重现
- `chongxian_gongyao_shuju()`: 重现存储的公钥数据

**工作流程**:
1. 创建实例时生成P-256密钥对
2. 生成包含时间戳的公钥数据(73字节)
3. 接收对方公钥后验证格式和时间戳(12小时有效期)
4. 使用ECDH算法计算共享密钥
5. 通过SHA256派生AES-256密钥

### 2. 前端ECDH密钥交换器 (`qianduan_ecdh_miyaojiaohuan`)

**用途**: 前端密钥交换组件，适配WASM环境，支持浏览器端密钥交换。

**性能特点**:
- 兼容WebAssembly环境
- 轻量级设计，适合浏览器运行
- 支持自定义客户端ID
- 内存占用约150字节/实例

**主要结构体**:
```rust
pub struct qianduan_ecdh_miyaojiaohuan {
    siyao: SecretKey,        // 私钥
    gongyao: PublicKey,      // 公钥
    kehuduan_id: String,     // 客户端标识
}
```

**核心方法**:
- `new()`: 创建实例，自动生成客户端ID
- `new_with_id()`: 使用指定客户端ID创建实例
- `chuangjian_miyao_qingqiu()`: 创建密钥交换请求
- `chuli_fuwuqi_xiangying()`: 处理服务器响应并计算共享密钥
- `get_kehuduan_id()`: 获取客户端ID

**工作流程**:
1. 创建实例并生成唯一客户端ID
2. 生成密钥交换请求(包含公钥+时间戳)
3. 发送请求到后端服务器
4. 接收服务器响应并验证公钥
5. 计算共享密钥并派生AES密钥

### 3. 密钥交换管理器 (`miyaojiaohuan_guanliqii`)

**用途**: 后端会话管理中心，负责会话生命周期管理、密钥重现、自动清理。

**性能特点**:
- 线程安全的HashMap存储
- 4小时会话超时机制
- 自动垃圾回收过期会话
- 支持并发访问，使用Arc<Mutex>保护

**主要结构体**:
```rust
pub struct miyaojiaohuan_guanliqii {
    huoye_huihua: Arc<Mutex<HashMap<String, miyaojiaohuan_huihua>>>, // 活跃会话
    huihua_chaoshi: u64, // 超时时间(14400秒=4小时)
}

pub struct miyaojiaohuan_huihua {
    ecdh: houduan_ecdh_miyaojiaohuan,           // ECDH实例
    gongxiang_miyao: Option<houduan_gongxiang_miyao>, // 共享密钥
    chuangjian_shijian: Instant,                // 创建时间
    kehuduan_id: Option<String>,                // 客户端ID
    zhuangtai: miyaojiaohuan_zhuangtai,        // 会话状态
}
```

**核心方法**:
- `new()`: 创建管理器实例
- `chuangjian_huihua()`: 创建新会话
- `chuli_miyao_jiaohuan()`: 处理密钥交换请求
- `chongxian_gongyao_shuju()`: 重现公钥数据
- `chongxian_jiaohuan_miyao()`: 重现并重新计算共享密钥
- `qingli_guoqi_huihua()`: 清理过期会话
- `get_huoye_huihua_shuliang()`: 获取活跃会话数量

**会话状态管理**:
```rust
pub enum miyaojiaohuan_zhuangtai {
    dengdai_kehuduan_gongyao,  // 等待客户端公钥
    wancheng_miyao_jiaohuan,   // 已完成密钥交换
    yiguoqi,                   // 已过期
}
```

**性能指标**:
- 单个会话内存占用: ~500字节
- 支持并发会话数: 10000+
- 会话查找时间复杂度: O(1)
- 清理操作时间复杂度: O(n)

### 4. 加密传输器 (`jiamichuanshu_gongneng`)

**用途**: 基于共享密钥的数据加密传输，支持前后端双向加密通信。

**性能特点**:
- 使用AES-256-GCM算法
- 每次加密生成随机nonce
- Base64编码传输
- 支持任意长度数据加密

**主要结构体**:
```rust
pub struct houduan_jiamichuanshu {
    gongxiang_miyao: houduan_gongxiang_miyao, // 共享密钥
    cipher: Aes256Gcm,                        // AES加密器
}

pub struct qianduan_jiamichuanshu {
    gongxiang_miyao: qianduan_gongxiang_miyao, // 共享密钥
    cipher: Aes256Gcm,                         // AES加密器
}
```

**加密消息格式**:
```rust
pub struct jiami_xiaoxi {
    jiami_shuju: String,    // Base64编码的加密数据
    suijishu: String,       // Base64编码的nonce
    huihua_id: String,      // 会话ID
    xiaoxi_id: String,      // 消息ID(UUID)
    shijian_chuo: u64,      // Unix时间戳
}
```

**核心方法**:
- `new()`: 基于共享密钥创建加密器
- `jiami()`: 加密字节数据
- `jiemi()`: 解密数据
- `jiami_zifuchuan()`: 加密字符串
- `jiemi_wei_zifuchuan()`: 解密为字符串

## 完整工作流程

### 密钥交换流程
```
1. 前端创建 qianduan_ecdh_miyaojiaohuan 实例
2. 前端生成密钥交换请求 (公钥+时间戳+客户端ID)
3. 前端发送HTTP请求到 /key-exchange 接口
4. 后端 miyaojiaohuan_guanliqii 处理请求:
   - 创建新会话
   - 验证客户端公钥格式和时间戳
   - 计算共享密钥
   - 存储公钥数据用于重现
   - 返回服务器公钥和会话ID
5. 前端接收响应并计算共享密钥
6. 双方获得相同的AES-256密钥用于后续通信
```

### 密钥重现流程
```
1. 客户端使用会话ID请求重现
2. 后端从HashMap中查找会话
3. 验证会话未过期(4小时内)
4. 从存储的ECDH实例中重现公钥数据
5. 重新计算共享密钥(如需要)
6. 返回重现结果
```

### 会话清理流程
```
1. 每次处理新请求时触发清理
2. 遍历所有活跃会话
3. 检查创建时间是否超过4小时
4. 删除过期会话释放内存
5. 保留有效会话继续服务
```

## HTTP接口规范

### 密钥交换接口
- **路径**: `POST /key-exchange`
- **请求格式**:
```json
{
  "kehuduan_gongyao": "hex_encoded_pubkey_with_timestamp",
  "kehuduan_id": "client_uuid",
  "shijian_chuo": 1640995200000
}
```
- **响应格式**:
```json
{
  "chenggong": true,
  "xiaoxi": "密钥交换成功",
  "shuju": {
    "fuwuqi_gongyao": "hex_encoded_server_pubkey",
    "huihua_id": "session_uuid",
    "zhuangtai": "success"
  }
}
```

## 安全特性

### 时间戳验证
- 公钥附带13位毫秒时间戳
- 12小时有效期防止重放攻击
- 前后端时间同步要求

### 密钥派生
- ECDH原始共享密钥 + SHA256派生
- 包含客户端ID和会话ID增强唯一性
- 生成32字节AES-256密钥

### 会话隔离
- 每个会话独立的密钥对
- UUID会话标识防止冲突
- 4小时自动过期机制

## 性能优化建议

### 内存管理
- 定期清理过期会话
- 限制最大并发会话数
- 使用对象池复用ECDH实例

### 计算优化
- 缓存公钥数据避免重复生成
- 预计算常用椭圆曲线参数
- 使用硬件加速(如支持)

### 网络优化
- 压缩传输数据
- 批量处理多个请求
- 实现连接池复用

## 扩展性设计

### 算法升级
- 支持多种椭圆曲线(P-384, P-521)
- 兼容后量子密码算法
- 可配置的密钥长度

### 功能扩展
- 支持密钥轮换
- 实现密钥托管
- 添加审计日志

### 部署扩展
- 支持集群部署
- 实现会话同步
- 添加负载均衡

## 故障排查

### 常见问题
1. **时间戳过期**: 检查前后端时间同步
2. **会话不存在**: 验证会话ID正确性和有效期
3. **公钥格式错误**: 检查hex编码和长度
4. **内存泄漏**: 监控会话清理机制

### 调试方法
- 启用详细日志记录
- 使用测试工具验证流程
- 监控性能指标
- 检查错误返回码

## 使用示例

### 前端使用示例
```rust
// 1. 创建前端密钥交换器
let qianduan_ecdh = qianduan_ecdh_miyaojiaohuan::new()?;

// 2. 生成密钥交换请求
let qingqiu = qianduan_ecdh.chuangjian_miyao_qingqiu()?;

// 3. 发送HTTP请求到后端
// ... HTTP请求代码 ...

// 4. 处理服务器响应
let gongxiang_miyao = qianduan_ecdh.chuli_fuwuqi_xiangying(&xiangying)?;

// 5. 创建加密传输器
let jiami_chuanshuqi = qianduan_jiamichuanshu::new(gongxiang_miyao)?;

// 6. 加密数据
let jiami_xiaoxi = jiami_chuanshuqi.jiami_zifuchuan("敏感数据")?;
```

### 后端使用示例
```rust
// 1. 创建会话管理器
let guanliqii = miyaojiaohuan_guanliqii::new();

// 2. 处理密钥交换请求
let xiangying = guanliqii.chuli_miyao_jiaohuan(&qingqiu)?;

// 3. 重现密钥(4小时内)
let chongxian_miyao = guanliqii.chongxian_jiaohuan_miyao(
    &huihua_id,
    &kehuduan_gongyao
)?;

// 4. 创建加密传输器
let jiami_chuanshuqi = houduan_jiamichuanshu::new(chongxian_miyao)?;

// 5. 解密数据
let jiemi_xiaoxi = jiami_chuanshuqi.jiemi(&jiami_xiaoxi)?;
```

## 版本历史

- **v1.0**: 基础ECDH密钥交换
- **v1.1**: 添加时间戳验证
- **v1.2**: 实现密钥重现功能
- **v2.0**: 会话管理和自动清理
- **v2.1**: 4小时会话超时优化

## 技术依赖

### 核心库
- `p256`: P-256椭圆曲线实现
- `aes-gcm`: AES-256-GCM加密算法
- `sha2`: SHA256哈希算法
- `chrono`: 时间处理
- `uuid`: UUID生成
- `serde`: 序列化/反序列化
- `anyhow`: 错误处理

### 性能特征
- **密钥生成**: ~1ms (P-256)
- **共享密钥计算**: ~0.5ms
- **AES加密**: ~0.1ms/KB
- **会话查找**: ~0.01ms
- **内存占用**: ~500字节/会话

---

**注意**: 本文档面向AI助手和开发者，提供系统架构的完整视图。在进行任何修改时，请确保理解各组件间的依赖关系和安全影响。修改建议：

1. **添加新功能时**: 先更新相关结构体，再实现方法，最后更新测试
2. **性能优化时**: 重点关注会话管理和密钥计算部分
3. **安全增强时**: 优先考虑时间戳验证和密钥派生机制
4. **扩展部署时**: 注意会话同步和负载均衡的实现