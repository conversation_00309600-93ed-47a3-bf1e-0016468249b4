# 日志系统文档总览

## 文档结构

本目录包含日志系统的完整文档，分为以下两个主要部分：

### 📖 [日志系统调用文档](./日志系统调用文档.md)
**适用对象：** 使用日志系统的开发者、AI助手

**内容包括：**
- 快速开始指南
- 完整的API参考
- 使用示例和最佳实践
- 常见使用场景
- 性能考虑和注意事项

**何时使用：**
- 需要了解如何调用日志系统API
- 寻找特定功能的使用方法
- 查看代码示例和最佳实践
- 解决使用过程中的问题

### 🏗️ [日志系统架构文档](./日志系统架构文档.md)
**适用对象：** 系统架构师、核心开发者、需要深入理解系统的AI助手

**内容包括：**
- 系统整体架构设计
- 各模块详细说明和职责
- 类结构和方法详解
- 数据流程和算法逻辑
- 设计模式和原则
- 性能优化策略
- 扩展性设计

**何时使用：**
- 需要理解系统内部实现
- 进行系统维护和扩展
- 排查复杂问题
- 学习系统设计思路

## 快速导航

### 🚀 我想快速使用日志系统
→ 查看 [日志系统调用文档](./日志系统调用文档.md) 的"快速开始"部分

### 🔍 我想了解特定API的用法
→ 查看 [日志系统调用文档](./日志系统调用文档.md) 的"API参考"部分

### 🛠️ 我想理解系统是如何工作的
→ 查看 [日志系统架构文档](./日志系统架构文档.md) 的"系统架构图"和"模块详细说明"

### 🐛 我遇到了问题需要排查
→ 查看 [日志系统架构文档](./日志系统架构文档.md) 的"故障排查指南"

### 📈 我想优化性能
→ 查看 [日志系统架构文档](./日志系统架构文档.md) 的"性能优化"部分

### 🔧 我想扩展功能
→ 查看 [日志系统架构文档](./日志系统架构文档.md) 的"扩展性设计"部分

## 系统概述

日志系统是一个高性能、异步的Rust日志库，具有以下特点：

### 核心特性
- ✅ **异步处理**：不阻塞主线程的日志写入
- ✅ **自动分类**：错误日志和正常日志自动分离
- ✅ **按日轮换**：按日期自动创建新的日志文件
- ✅ **输出模式**：支持静默模式和详细模式
- ✅ **线程安全**：支持多线程环境使用
- ✅ **宏支持**：提供便捷的格式化宏
- ✅ **零配置**：开箱即用，无需复杂配置

### 技术栈
- **语言**：Rust
- **异步运行时**：Tokio
- **时间处理**：Chrono
- **错误处理**：Anyhow

### 文件结构
```
./rizhi/                    # 日志根目录
├── cuowu/                  # 错误日志目录
│   └── 2025-07-26.log     # 按日期命名的错误日志
└── zhengchang/             # 正常日志目录
    └── 2025-07-26.log     # 按日期命名的正常日志
```

### 日志级别
- **INFO** (信息)：一般信息记录
- **WARN** (警告)：警告信息
- **ERROR** (错误)：错误信息（自动分离到错误目录）
- **DEBUG** (调试)：调试信息

### 输出模式
- **静默模式** (`jinmo`)：只写入日志文件
- **详细模式** (`xiangxi`)：写入日志文件 + 打印到控制台

## 使用示例

### 基础使用
```rust
// 初始化
rizhixitong_chushihua_rizhixitong().await?;

// 记录日志
rizhixitong::rizhixitong_xinxi("应用启动成功");
rizhixitong::rizhixitong_cuowu("数据库连接失败");
```

### 带输出模式
```rust
use rizhixitong::rizhixitong_shuchu_moshi;

// 静默模式（只写文件）
rizhixitong::rizhixitong_xinxi_with_moshi(
    "后台任务完成", 
    rizhixitong_shuchu_moshi::jinmo
);

// 详细模式（写文件+控制台）
rizhixitong::rizhixitong_cuowu_with_moshi(
    "支付失败", 
    rizhixitong_shuchu_moshi::xiangxi
);
```

### 使用宏
```rust
// 基础宏
rizhixitong_rizhi_xinxi!("用户 {} 登录", username);

// 带输出模式的宏
rizhixitong_rizhi_cuowu_with_moshi!(
    rizhixitong_shuchu_moshi::xiangxi,
    "错误代码: {}, 原因: {}", 
    error_code, 
    error_msg
);
```

## 模块组织

```
src/rizhixitong/
├── mod.rs                      # 模块导出
├── rizhixitong_chushihua.rs    # 初始化和公共API
├── rizhixitong_xieruqi.rs      # 核心写入器
├── rizhixitong_fenlei.rs       # 日志分类管理
└── rizhixitong_lunhuan.rs      # 文件轮换管理
```

## 设计原则

1. **简单易用**：提供简洁的API，隐藏复杂的内部实现
2. **高性能**：异步处理，不阻塞主线程
3. **可靠性**：错误处理完善，系统稳定
4. **可扩展**：模块化设计，易于扩展新功能
5. **线程安全**：支持多线程并发使用

## 版本信息

- **当前版本**：v2.0
- **最后更新**：2025-07-26
- **兼容性**：Rust 1.70+, Tokio 1.0+

## 贡献指南

如需修改或扩展日志系统：

1. 阅读 [架构文档](./日志系统架构文档.md) 了解系统设计
2. 遵循现有的命名规范和代码风格
3. 确保新功能有对应的测试
4. 更新相关文档

## 联系方式

如有问题或建议，请通过以下方式联系：
- 查看文档中的故障排查指南
- 检查代码注释和示例
- 参考测试用例了解用法

---

**提示：** 建议先阅读调用文档快速上手，再根据需要深入了解架构文档。
