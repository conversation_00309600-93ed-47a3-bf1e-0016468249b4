# 日志系统调用文档

## 概述

本文档详细介绍了日志系统的所有调用方法，包括基础API、带输出模式的API、宏定义以及使用示例。

## 快速开始

### 1. 初始化日志系统

```rust
use rizhixitong::rizhixitong_chushihua_rizhixitong;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 使用默认路径 "./rizhi" 初始化
    rizhixitong_chushihua_rizhixitong().await?;
    
    // 或者指定自定义路径
    // rizhixitong_chushihua_rizhixitong_with_path("/path/to/logs").await?;
    
    Ok(())
}
```

### 2. 基础日志记录

```rust
use rizhixitong;

// 记录不同级别的日志
rizhixitong::rizhixitong_xinxi("应用程序启动成功");
rizhixitong::rizhixitong_jinggao("这是一个警告消息");
rizhixitong::rizhixitong_cuowu("这是一个错误消息");
rizhixitong::rizhixitong_tiaoshi("这是一个调试消息");
```

## API 参考

### 基础日志函数

#### `rizhixitong_xinxi(neirong: &str)`
记录信息级别日志（静默模式，只写文件）

**参数：**
- `neirong`: 日志内容

**示例：**
```rust
rizhixitong::rizhixitong_xinxi("用户登录成功");
```

#### `rizhixitong_jinggao(neirong: &str)`
记录警告级别日志（静默模式，只写文件）

**参数：**
- `neirong`: 日志内容

**示例：**
```rust
rizhixitong::rizhixitong_jinggao("内存使用率较高");
```

#### `rizhixitong_cuowu(neirong: &str)`
记录错误级别日志（静默模式，只写文件）

**参数：**
- `neirong`: 日志内容

**示例：**
```rust
rizhixitong::rizhixitong_cuowu("数据库连接失败");
```

#### `rizhixitong_tiaoshi(neirong: &str)`
记录调试级别日志（静默模式，只写文件）

**参数：**
- `neirong`: 日志内容

**示例：**
```rust
rizhixitong::rizhixitong_tiaoshi("处理请求ID: 12345");
```

### 带输出模式的日志函数

#### 输出模式枚举

```rust
use rizhixitong::rizhixitong_shuchu_moshi;

// 静默模式：只写入日志文件
rizhixitong_shuchu_moshi::jinmo

// 详细模式：写入日志文件 + 打印到控制台
rizhixitong_shuchu_moshi::xiangxi
```

#### `rizhixitong_xinxi_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi)`
记录信息级别日志，可指定输出模式

**参数：**
- `neirong`: 日志内容
- `shuchu_moshi`: 输出模式

**示例：**
```rust
use rizhixitong::{rizhixitong_shuchu_moshi};

// 静默模式（只写文件）
rizhixitong::rizhixitong_xinxi_with_moshi("后台任务完成", rizhixitong_shuchu_moshi::jinmo);

// 详细模式（写文件+控制台输出）
rizhixitong::rizhixitong_xinxi_with_moshi("用户操作记录", rizhixitong_shuchu_moshi::xiangxi);
```

#### `rizhixitong_jinggao_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi)`
记录警告级别日志，可指定输出模式

#### `rizhixitong_cuowu_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi)`
记录错误级别日志，可指定输出模式

#### `rizhixitong_tiaoshi_with_moshi(neirong: &str, shuchu_moshi: rizhixitong_shuchu_moshi)`
记录调试级别日志，可指定输出模式

## 宏定义

### 基础宏

#### `rizhixitong_rizhi_xinxi!(format_str, ...)`
格式化信息日志宏（静默模式）

**示例：**
```rust
rizhixitong_rizhi_xinxi!("用户 {} 登录成功，IP: {}", "张三", "*************");
```

#### `rizhixitong_rizhi_jinggao!(format_str, ...)`
格式化警告日志宏（静默模式）

#### `rizhixitong_rizhi_cuowu!(format_str, ...)`
格式化错误日志宏（静默模式）

#### `rizhixitong_rizhi_tiaoshi!(format_str, ...)`
格式化调试日志宏（静默模式）

### 带输出模式的宏

#### `rizhixitong_rizhi_xinxi_with_moshi!(shuchu_moshi, format_str, ...)`
格式化信息日志宏，可指定输出模式

**示例：**
```rust
rizhixitong_rizhi_xinxi_with_moshi!(
    rizhixitong_shuchu_moshi::xiangxi, 
    "处理订单 {} 成功，金额: {}", 
    "ORD001", 
    299.99
);
```

#### `rizhixitong_rizhi_jinggao_with_moshi!(shuchu_moshi, format_str, ...)`
格式化警告日志宏，可指定输出模式

#### `rizhixitong_rizhi_cuowu_with_moshi!(shuchu_moshi, format_str, ...)`
格式化错误日志宏，可指定输出模式

#### `rizhixitong_rizhi_tiaoshi_with_moshi!(shuchu_moshi, format_str, ...)`
格式化调试日志宏，可指定输出模式

## 日志文件结构

日志文件按以下结构组织：

```
./rizhi/
├── cuowu/           # 错误日志目录
│   └── 2025-07-26.log
└── zhengchang/      # 正常日志目录（信息、警告、调试）
    └── 2025-07-26.log
```

### 日志格式

```
[2025-07-26 08:49:25.680] [INFO] 应用程序启动成功
[2025-07-26 08:49:25.680] [WARN] 这是一个警告消息
[2025-07-26 08:49:25.680] [ERROR] 这是一个错误消息
[2025-07-26 08:49:25.680] [DEBUG] 这是一个调试消息
```

格式说明：
- `[时间戳]`: 精确到毫秒的时间戳
- `[级别]`: 日志级别（INFO/WARN/ERROR/DEBUG）
- `内容`: 实际的日志内容

## 使用场景和最佳实践

### 1. 静默模式使用场景
- 后台定时任务
- 系统内部状态记录
- 性能监控数据
- 不需要实时查看的日志

```rust
// 后台任务完成
rizhixitong::rizhixitong_xinxi_with_moshi("定时清理任务完成", rizhixitong_shuchu_moshi::jinmo);

// 系统监控
rizhixitong_rizhi_xinxi_with_moshi!(
    rizhixitong_shuchu_moshi::jinmo,
    "CPU使用率: {}%, 内存使用率: {}%", 
    cpu_usage, 
    memory_usage
);
```

### 2. 详细模式使用场景
- 用户操作记录
- 重要的业务流程
- 错误和异常情况
- 开发调试阶段

```rust
// 用户重要操作
rizhixitong::rizhixitong_xinxi_with_moshi("用户支付成功", rizhixitong_shuchu_moshi::xiangxi);

// 错误情况
rizhixitong_rizhi_cuowu_with_moshi!(
    rizhixitong_shuchu_moshi::xiangxi,
    "支付失败: 错误代码 {}, 原因: {}", 
    error_code, 
    error_message
);
```

### 3. 性能考虑
- 日志系统使用异步通道，不会阻塞主线程
- 文件写入使用缓冲区，提高性能
- 按日期和类型自动分割日志文件

### 4. 错误处理
- 如果日志系统未初始化，会在stderr输出错误信息
- 文件写入失败时会在stderr输出错误信息
- 通道关闭时会在stderr输出错误信息

## 完整示例

```rust
mod rizhixitong;

use rizhixitong::{rizhixitong_chushihua_rizhixitong, rizhixitong_shuchu_moshi};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 1. 初始化日志系统
    rizhixitong_chushihua_rizhixitong().await?;

    // 2. 基础日志记录（静默模式）
    rizhixitong::rizhixitong_xinxi("应用程序启动");
    rizhixitong::rizhixitong_jinggao("配置文件版本较旧");
    
    // 3. 使用宏记录格式化日志
    rizhixitong_rizhi_xinxi!("加载配置文件: {}", "config.toml");
    
    // 4. 带输出模式的日志记录
    rizhixitong::rizhixitong_xinxi_with_moshi(
        "用户登录成功", 
        rizhixitong_shuchu_moshi::xiangxi
    );
    
    // 5. 带输出模式的宏
    rizhixitong_rizhi_cuowu_with_moshi!(
        rizhixitong_shuchu_moshi::xiangxi,
        "数据库连接失败: {}", 
        "连接超时"
    );

    // 等待日志写入完成
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    Ok(())
}
```

## 注意事项

1. **必须先初始化**：在使用任何日志功能前，必须先调用初始化函数
2. **异步环境**：日志系统设计为在tokio异步环境中使用
3. **文件权限**：确保程序有权限在指定目录创建和写入文件
4. **磁盘空间**：长期运行的应用需要考虑日志文件的磁盘占用
5. **线程安全**：所有日志函数都是线程安全的，可以在多线程环境中使用
