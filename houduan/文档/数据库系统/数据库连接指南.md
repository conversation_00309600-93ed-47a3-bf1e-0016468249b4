# 数据库连接指南

本文档详细说明了如何在应用程序中配置和初始化数据库连接。

## 概述

数据库系统的连接管理依赖于配置系统 (`peizhixitong`)。所有的数据库连接信息（如 IP 地址、用户名、密码）都应在 `zongpeizhi.yml` 文件中进行配置。

系统提供了独立的模块来管理 `MySQL` 和 `Redis` 的连接。

## 1. 配置数据库连接

在 `zongpeizhi.yml` 文件中，你需要配置 `shujuku` 字段，如下所示：

```yaml
# ./peizhi/zongpeizhi.yml

shujuku:
  shujukuip: 127.0.0.1
  yonghu: root
  mima: 'your_mysql_password'
  redis_dizhi: "redis://127.0.0.1/"
```

- `shujukuip`, `yonghu`, `mima` 用于 `MySQL` 连接。
- `redis_dizhi` 用于 `Redis` 连接。

## 2. 初始化和获取 MySQL 连接

`MySQL` 的连接是通过一个连接池 (`Pool`) 来管理的，以实现高效复用。

### 示例

```rust
// 假设 peizhi_guanli 是一个已经初始化好的 peizhixitong_guanli 实例
let peizhi = peizhi_guanli.peizhixitong_get_peizhi().unwrap();

// 1. 初始化 MySQL 连接池
let mysql_chihua = match mysql_chushihua::chushihua(&peizhi.shujuku) {
    Ok(chihua) => {
        println!("MySQL 连接池初始化成功");
        chihua
    }
    Err(e) => {
        panic!("无法初始化 MySQL 连接池: {}", e);
    }
};

// 2. 从连接池获取一个连接
match mysql_lianjie::get_lianjie(&mysql_chihua) {
    Ok(lianjie) => {
        println!("成功获取 MySQL 连接！");
        // 在这里使用 lianjie 进行数据库操作
    }
    Err(e) => {
        eprintln!("获取 MySQL 连接失败: {}", e);
    }
}```

### 关键点

- **初始化**: `mysql_chushihua::chushihua()` 方法会根据配置创建一个 `MySQL` 连接池。该操作应在程序启动时执行一次。
- **获取连接**: `mysql_lianjie::get_lianjie()` 方法从连接池中获取一个可用的连接。使用完毕后，连接会自动返回池中。

## 3. 初始化和获取 Redis 连接

`Redis` 的连接管理相对简单，通常是获取一个客户端实例，然后通过它获取连接。

### 示例

```rust
// 假设 peizhi_guanli 是一个已经初始化好的 peizhixitong_guanli 实例
let peizhi = peizhi_guanli.peizhixitong_get_peizhi().unwrap();

// 1. 初始化 Redis 客户端
let redis_kehuan = match redis_chushihua::chushihua(&peizhi.shujuku) {
    Ok(kehuan) => {
        println!("Redis 客户端初始化成功");
        kehuan
    }
    Err(e) => {
        panic!("无法初始化 Redis 客户端: {}", e);
    }
};

// 2. 从客户端获取一个连接
match redis_lianjie::get_lianjie(redis_kehuan) {
    Ok(mut lianjie) => {
        println!("成功获取 Redis 连接！");
        // 在这里使用 lianjie 进行 Redis 操作
        // 例如: let _ : () = redis::cmd("SET").arg("my_key").arg(42).query(&mut lianjie).unwrap();
    }
    Err(e) => {
        eprintln!("获取 Redis 连接失败: {}", e);
    }
}```

### 关键点

- **初始化**: `redis_chushihua::chushihua()` 方法根据配置创建一个 `Redis` 客户端。
- **获取连接**: `redis_lianjie::get_lianjie()` 方法从客户端获取一个连接。`Redis` 连接在使用后也需要妥善管理。