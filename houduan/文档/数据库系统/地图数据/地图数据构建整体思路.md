好的，现在我需要制作一个地图数据的方法，这个方法我会传入我需要获取的地图id，以及我需要获取的字段名字
你可以从地图数据结构.md中看到我两个表中的字段和表的名字
首先是地图id，地图id不一定是数字，他可能是其他的字母等等，然后我有两个表，一个是huizong一个是name，我可以告诉你name表是一个从表，所以如果查询单个字段，那只会考虑huizong表的字段
但是如果我传入的是quanbu_xinxi，那么就是获取全部信息，所有字段的内容，这样就可以从这两个表当中去获取了，就是全部数据下才需要两个表去获取
然后是redis是存储3天，但是注意，获取单个字段是不会记录redis的，只有获取全部的数据才会记录redis
redis和日志以及数据结构还有sql命令我都创建了类进行统一管理，具体你可以看一下这个包的内容，就是我不希望我的ditushujuchuli.rs里面有硬编码！
包括redis的缓存时间sql日志等等，然后是redis，我希望你能够创建一个方法，这个方法可以单独删除掉你上面所获取的数据的缓存而不会影响到其他的缓存，就是只删除这个地图的全部信息，但是不会删除怪物的，或者地图其他的缓存

好的，现在我需要制作的是，地图的列表，同样的，结构你需要放到结构体类中，就是我传入一页有多少个地图，以及现在是多少页，就可以显示地图列表，一个地图在列表中构成是：
id，和地图名字，还有它的分类，字段是：
| leixing_town | text | YES | | NULL | | 类型-城镇 |
| leixing_field | text | YES | | NULL | | 类型-野外 |
| leixing_dungeon | text | YES | | NULL | | 类型-地下城 |
| leixing_quest | text | YES | | NULL | | 类型-任务 |
| leixing_instance | text | YES | | NULL | | 类型-副本 |
| leixing_siege | text | YES | | NULL | | 类型-攻城 |
| leixing_pvp | text | YES | | NULL | | 类型-PVP |
| leixing_other | text | YES | | NULL | | 类型-其他 |
| leixing_weizhi | text | YES | | NULL | | 类型-位置 |
| fenlei_town | text | YES | | NULL | | 分类-城镇 |
| fenlei_field | text | YES | | NULL | | 分类-野外 |
| fenlei_dungeon | text | YES | | NULL | | 分类-地下城 |
| fenlei_quest | text | YES | | NULL | | 分类-任务 |
| fenlei_instance | text | YES | | NULL | | 分类-副本 |
| fenlei_siege | text | YES | | NULL | | 分类-攻城 |
| fenlei_pvp | text | YES | | NULL | | 分类-PVP |
| fenlei_other | text | YES | | NULL | | 分类-其他 |
| fenlei_weizhi | text | YES | | NULL | | 分类-未知 |

这些都在huizong表中的，列表当中会检测她们的这些字段是true还是false，如果是true就加到一个嵌套的json内
也就是：
地图id
地图名字
地图分类{

}
构成一个地图在列表中的显示

然后是地图名字，地图名字的话我们拿到id的第一时间就去name表中去查询，name表中的name字段就是地图的名字，如果name表找不到，我们再从huizong表中去查询，huizong表中的ditu_mingcheng字段就是地图的名字
也就是在名字上，地图名字的优先级是name表中的name字段 > huizong表中的ditu_mingcheng字段
然后在列表的最末嵌套中，加上当前页数和全部页数还有一共有多少个地图还有当前页面有多少个地图

#### 2. ditu_name (地图名称表)

| 字段名  | 数据类型 | 允许空值 | 键 | 默认值  | 额外信息 | 说明   |
|------|------|------|---|------|------|------|
| id   | text | YES  |   | NULL |      | 地图ID |
| name | text | YES  |   | NULL |      | 地图名称 |

然后redis的缓存时间是1个小时，注意，你需要在redis控制类创建一个方法，这个方法用于主动删除缓存，但是她只能删除关于地图列表的缓存，而不会影响到其他的缓存
