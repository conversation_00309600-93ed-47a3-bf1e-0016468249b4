# 数据库系统架构

本文档详细描述了数据库系统的内部架构，包括各个模块、结构体和核心方法的职责。

## 核心模块

数据库系统由以下三个核心模块组成：

- **`mysqlshujuku`**: 负责与 `MySQL` 数据库的交互，包括连接管理和数据操作。
- **`redishujuku`**: 负责与 `Redis` 数据库的交互，用于缓存和高速数据存取。
- **`shujuku_rizhiguanli`**: 提供统一的日志记录功能，方便调试和监控数据库相关操作。

---

## 1. `mysqlshujuku` - MySQL 数据库模块

该模块负责管理与 `MySQL` 数据库的所有交互。

- **子模块**:
  - `mysql_chushihua`: 负责 `MySQL` 连接的初始化和检查。
  - `mysql_lianjie`: 提供获取数据库连接池的方法。
  - `mysql_shujuku_guanli`: 封装了对数据库的常用操作。
  - `shujuku_neirong`: 定义了数据库中的表结构，如 `yonghu_shujuku_biao`。

---

## 2. `redishujuku` - Redis 数据库模块

该模块负责管理与 `Redis` 数据库的连接和操作，主要用于缓存游戏数据。

- **子模块**:
  - `redis_chushihua`: 负责 `Redis` 的初始化。
  - `redis_lianjie`: 提供获取 `Redis` 连接的方法。

---

## 3. `shujuku_rizhiguanli` - 数据库日志管理模块

提供一套统一的日志接口，用于记录数据库系统的运行状态。

### 主要方法

- **`shujukuxitong_rizhi_xinxi(mokuai_ming: &str, xiaoxi: &str)`**: 记录信息级别的日志。
- **`shujukuxitong_rizhi_jinggao(mokuai_ming: &str, xiaoxi: &str)`**: 记录警告级别的日志。
- **`shujukuxitong_rizhi_cuowu(mokuai_ming: &str, xiaoxi: &str)`**: 记录错误级别的日志。
- **`shujukuxitong_rizhi(mokuai_ming: &str, xiaoxi: &str)`**: 根据消息内容自动判断日志级别。