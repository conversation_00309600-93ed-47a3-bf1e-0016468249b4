=== guaiwu_huizong 表结构 ===
字段: id, 类型: , 允许空值: YES, 键: , 额外: 
字段: dbname, 类型: , 允许空值: YES, 键: , 额外: 
字段: z<PERSON><PERSON><PERSON>, 类型: , 允许空值: YES, 键: , 额外: 
字段: chicun_small, 类型: , 允许空值: YES, 键: , 额外: 
字段: chicun_medium, 类型: , 允许空值: YES, 键: , 额外: 
字段: chicun_large, 类型: , 允许空值: YES, 键: , 额外: 
字段: chicun_weizhi, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_wu, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_shui, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_di, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_huo, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_feng, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_du, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_sheng, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_an, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_nian, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_busi, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansu_weizhi, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_formless, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_undead, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_brute, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_plant, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_insect, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_fish, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_demon, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_human, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_angel, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_dragon, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhongzu_weizhi, 类型: , 允许空值: YES, 键: , 额外: 
字段: biaozhi_normal, 类型: , 允许空值: YES, 键: , 额外: 
字段: biaozhi_champion, 类型: , 允许空值: YES, 键: , 额外: 
字段: biaozhi_boss, 类型: , 允许空值: YES, 键: , 额外: 
字段: biaozhi_mvp, 类型: , 允许空值: YES, 键: , 额外: 
字段: biaozhi_weizhi, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_aggressive, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_assist, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_looter, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_cast_sensor, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_immobile, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_weizhi, 类型: , 允许空值: YES, 键: , 额外: 
字段: level, 类型: , 允许空值: YES, 键: , 额外: 
字段: health, 类型: , 允许空值: YES, 键: , 额外: 
字段: base_experience, 类型: , 允许空值: YES, 键: , 额外: 
字段: job_experience, 类型: , 允许空值: YES, 键: , 额外: 
字段: mvp_flag, 类型: , 允许空值: YES, 键: , 额外: 
字段: jichuxinxi_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: zhushuxing_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: ai_behaviors_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: yuansukangxing_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: fenlei_xinxi_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: putong_diaoluowu_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: mvp_diaoluowu_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: ditu_diaoluowu_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: jineng_liebiao_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: ditu_liebiao_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: jingyan_xinxi_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: renwu_liebiao_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: chongwu_xinxi_yaml, 类型: , 允许空值: YES, 键: , 额外: 
字段: diaoluowu_shuliang, 类型: , 允许空值: YES, 键: , 额外: 
字段: jineng_shuliang, 类型: , 允许空值: YES, 键: , 额外: 
字段: ditu_shuliang, 类型: , 允许空值: YES, 键: , 额外: 
字段: renwu_shuliang, 类型: , 允许空值: YES, 键: , 额外: 
字段: shi_chongwu, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_jichuxinxi, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_diaoluowu, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_jineng, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_ditu, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_jingyan, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_renwu, 类型: , 允许空值: YES, 键: , 额外: 
字段: you_chongwu, 类型: , 允许空值: YES, 键: , 额外: 
字段: jingyingguai, 类型: , 允许空值: YES, 键: , 额外: 

=== mob_name 表结构 ===
字段: ID, 类型: , 允许空值: YES, 键: , 额外: 
字段: Aegis_name, 类型: , 允许空值: YES, 键: , 额外: 
字段: Type, 类型: , 允许空值: YES, 键: , 额外: 
字段: schinese, 类型: , 允许空值: YES, 键: , 额外: 
字段: tchinese, 类型: , 允许空值: YES, 键: , 额外: 
字段: en, 类型: , 允许空值: YES, 键: , 额外: 
字段: jp, 类型: , 允许空值: YES, 键: , 额外: 
字段: kr, 类型: , 允许空值: YES, 键: , 额外: 
