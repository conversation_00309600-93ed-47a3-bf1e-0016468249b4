# 技能数据框架结构

## 数据库表结构

### 1. jineng_huizong 表（技能汇总表）

| 字段名                    | 字段类型 | 是否可空 | 键类型 | 默认值  | 额外信息 | 说明       |
|------------------------|------|------|-----|------|------|----------|
| jineng_id              | int  | YES  |     | NULL |      | 技能ID     |
| jineng_mingcheng       | text | YES  |     | NULL |      | 技能名称     |
| wupin_shuliang         | int  | YES  |     | NULL |      | 物品数量     |
| zhiye_shuliang         | int  | YES  |     | NULL |      | 职业数量     |
| lishibiandong_shuliang | int  | YES  |     | NULL |      | 历史变动数量   |
| zhongwenming           | text | YES  |     | NULL |      | 中文名      |
| leiming                | text | YES  |     | NULL |      | 类名       |
| tupian_url             | text | YES  |     | NULL |      | 图片URL    |
| zuida_dengji           | int  | YES  |     | NULL |      | 最大等级     |
| jichuxinxi_yaml        | text | YES  |     | NULL |      | 基础信息YAML |
| wupin_yaml             | text | YES  |     | NULL |      | 物品YAML   |
| zhiye_yaml             | text | YES  |     | NULL |      | 职业YAML   |
| lishibiandong_yaml     | text | YES  |     | NULL |      | 历史变动YAML |
| you_jichuxinxi         | text | YES  |     | NULL |      | 有基础信息    |
| you_wupin              | text | YES  |     | NULL |      | 有物品      |
| you_zhiye              | text | YES  |     | NULL |      | 有职业      |
| you_lishibiandong      | text | YES  |     | NULL |      | 有历史变动    |

### 2. skill_name 表（技能名称表）

| 字段名        | 字段类型 | 是否可空 | 键类型 | 默认值  | 额外信息 | 说明      |
|------------|------|------|-----|------|------|---------|
| ID         | int  | YES  |     | NULL |      | 技能ID    |
| Aegis_name | text | YES  |     | NULL |      | Aegis名称 |
| name       | text | YES  |     | NULL |      | 英文名称    |
| schinese   | text | YES  |     | NULL |      | 简体中文名称  |
| tchinese   | text | YES  |     | NULL |      | 繁体中文名称  |
| jp         | text | YES  |     | NULL |      | 日文名称    |
| kr         | text | YES  |     | NULL |      | 韩文名称    |

## 表关系说明

- `jineng_huizong` 表是技能的主要汇总表，包含技能的详细信息和各种YAML配置
- `skill_name` 表主要用于存储技能的多语言名称映射
- 两个表通过技能ID进行关联（jineng_huizong.jineng_id 对应 skill_name.ID）

## 数据特点

1. **多语言支持**：skill_name表支持多种语言的技能名称
2. **YAML配置**：jineng_huizong表使用YAML格式存储复杂的配置信息
3. **统计信息**：包含物品、职业、历史变动等相关数量统计
4. **标识字段**：使用"you_"前缀的字段标识是否包含某类信息