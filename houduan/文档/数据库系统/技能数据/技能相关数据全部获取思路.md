是这样的，现在我想要制作一个技能相关的数据获取，首先我们需要先知道技能有多少个表，你可以看一下
你可以看一下技能数据框架结构.md这个文件，里面有对应的表的名字以及她们的字段
我要做的就是能够去把这些数据获取出来然后接入我们的redis
具体要怎么做呢，这个方法是这样的
就是他可以传入字段，就是这个技能表任何的一个字段都是可以的，注意是huizong表不是name表，就是任何一个huizong表中的字段都是可以获取的，具体你可以参考一下
@wupinshujuchuli.rs这个类
如果是传入quanbu_xinxi的话，那么就可以获取huizong表和name表的这个技能的全部信息，注意只能通过id进行索引，所以是要求传入对应的id的
redis存储是3天
日志的话你需要全部接入到一个类进行统一管理
创建
jineng_redis_kongzhi.rs类来管理技能redis方面，在里面设定缓存时间，以及创建一个方法，这个方法可以单独删除掉你上面所获取的数据的缓存而不会影响到其他，并且只有在获取全部数据的时候才会创建缓存，不获取全部数据，就是获取单个字段的数据是不缓存的
关于redis的方法全部都防在这个redis控制类中

然后再创建一个jineng_rizhi_kongzhi.rs
这个类关于所有的硬编码日志，就是把她们统一放到这个类中进行统一管理
然后创建一个
jinengshujujiegouti.rs
这个数据用于存放一些你需要的比如说字段名字这些结构体

我发现你把一些常用的sql也放到日志类了，你需要修改一下，或许你可以增加一个
jineng_sql_kongzhi
用来把硬编码的sql命令集中管理起来
然后我发现你在bin和在jinnegshuju包中都创建了一个测试类，有没有关联？如果没有的话我希望你删除一个
并且你的测试中存在
测试5: 获取缓存统计信息
✓ 缓存统计信息:
未启用Redis缓存

的情况，也就是并没有加载好redis？你需要认真检查一下

很抱歉打断你，我希望你的测试，能够返回原始信息，这样子能够让我知道你的设计是否符合我需要的架构，而不是现在的那种接写后的结果

好的，现在我们来制作新的功能，简单来说他是一个技能列表功能，他支持分页查询，需要传入的内容是：
一页有多少个技能，现在是第几页，就可以获取技能列表了，redis的缓存是5个小时，这些还有日志是归给日志类和redis的控制类来管理的
同样对于redis你需要提供一个方法，这个方法能够只删除技能列表的缓存而不会影响到其他！
对于技能列表你也知道我们有两个表一个是huizong表一个是name表，我要告诉你的是name表是一个从表，你可以理解为他是一个翻译补丁，技能的名字我们是以他为优先的，但是列表的获取是huizong表
也就是我们从huizong表获取列表，然后你可以看看数据结构的那个md文件，这个文件里面是有对应的表的字段的，我们需要的对应技能的id，名称还有类名就这三个就可以构成这个物品在列表中的呈现了
最重要的是名称，名称在huizong表中是jineng_mingcheng字段，但是在name中是schinese，我们拿到id之后优先去name中拿名字，如果name表没有名字，就从huizong表的那个字段拿名字就可以了
然后最后记得就是列表嵌套的最末尾要有当前页数和全部页数还有一共有多少个技能还有当前页面有多少个技能

我看到还有一些信息如                            "技能列表缓存统计：\n- 技能列表缓存数量：{} 个\n- 缓存有效期：5小时",
没有移动到统一控制当中，以及redis类中的一些日志也没有统一移动到管理类！或许你应该细细检查一下了！