技能数据处理包结构分析

1. jinengshujujiegouti.rs - 数据结构定义类
   定义了技能数据处理的核心数据结构：
   jineng_jiben_xinxi - skill_name表基础信息结构体
   jineng_huizong_xinxi - jineng_huizong表汇总信息结构体
   jineng_wanzheng_xinxi - 完整技能信息结构体
   jineng_chaxun_jieguo - 技能查询结果结构体
   jineng_ziduan_yingshe - 技能字段映射管理类
2. jineng_sql_kongzhi.rs - SQL语句管理类
   jineng_sql_guanli 类负责管理所有技能相关的SQL语句：
   基础查询SQL（查询skill_name表和jineng_huizong表）
   存在性检查SQL
   动态SQL生成方法
   复杂查询SQL（统计、分页、搜索等）
3. jineng_redis_kongzhi.rs - Redis缓存控制类
   jineng_redis_kongzhi 类负责技能数据的Redis缓存管理：
   从Redis获取技能全部信息
   将技能信息存储到Redis（3天缓存）
   删除指定技能的缓存
   清理技能缓存
   获取缓存统计信息
4. jineng_rizhi_kongzhi.rs - 字符串常量管理类
   jineng_zifuchuan_changliangguanli 类管理技能数据处理的所有常量：
   查询模式常量
   数据库表名常量
   错误信息常量
   Redis相关常量
   错误信息生成方法
   Redis键名生成方法
5. jinengshujuchuli.rs - 核心数据管理类
   jineng_shuju_guanli 是技能数据处理的核心类：
   通用查询方法（支持全部信息查询和指定字段查询）
   查询技能全部信息（带Redis缓存）
   查询指定字段（只查询汇总表）
   批量查询技能
   检查技能是否存在
   缓存管理功能
6. jinengceshi.rs - 测试类
   jineng_ceshi 类提供技能数据处理功能的测试：
   测试技能数据获取功能
   测试技能缓存功能
   性能测试（对比缓存前后的查询速度）
7. mod.rs - 模块导出文件
   定义了包的公共接口，导出所有子模块。
   整体架构特点
   这个包采用了分层架构设计：
   数据层：jinengshujujiegouti.rs 定义数据结构
   持久层：jineng_sql_kongzhi.rs 管理数据库操作
   缓存层：jineng_redis_kongzhi.rs 管理Redis缓存
   业务层：jinengshujuchuli.rs 实现核心业务逻辑
   工具层：jineng_rizhi_kongzhi.rs 提供常量和工具方法
   测试层：jinengceshi.rs 提供功能测试