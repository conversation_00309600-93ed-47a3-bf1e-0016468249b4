# 依赖项文档

## aes
- **版本**: 0.8.4
- **描述**: 提供 AES（高级加密标准）密码的纯 Rust 实现。
- **特性**: 无

## aes-gcm
- **版本**: 0.10.3
- **描述**: 提供 AES-GCM（伽罗瓦/计数器模式）加密的实现，这是一种广泛使用的认证加密算法。
- **特性**: 无

## anyhow
- **版本**: 1.0.98
- **描述**: 一个灵活的库，用于在 Rust 应用程序中进行全面的错误处理。
- **特性**: 无

## base64
- **版本**: 0.22.1
- **描述**: 提供 base64 编码和解码的功能。
- **特性**: 无

## bcrypt
- **版本**: 0.17.0
- **描述**: 提供 bcrypt 哈希函数的实现，用于安全地存储密码。
- **特性**: 无

## chrono
- **版本**: 0.4.41
- **描述**: 一个用于处理日期和时间的库。
- **特性**: `serde`

## config
- **版本**: 0.15.13
- **描述**: 一个分层的配置系统，可以从多种来源（文件、环境变量等）读取配置。
- **特性**: 无

## dotenv
- **版本**: 0.15.0
- **描述**: 从 `.env` 文件中加载环境变量。
- **特性**: 无

## elliptic-curve
- **版本**: 0.13.8
- **描述**: 提供用于椭圆曲线密码学的通用特性和类型。
- **特性**: `ecdh`

## env_logger
- **版本**: 0.11.8
- **描述**: 一个配置 `log` crate 的日志记录器，可以通过环境变量进行配置。
- **特性**: 无

## hex
- **版本**: 0.4.3
- **描述**: 提供十六进制编码和解码的功能。
- **特性**: 无

## log
- **版本**: 0.4.27
- **描述**: 一个轻量级的日志记录外观，提供了通用的日志 API。
- **特性**: 无

## p256
- **版本**: 0.13.2
- **描述**: 提供 NIST P-256 椭圆曲线的纯 Rust 实现。
- **特性**: `ecdh`, `serde`

## rand
- **版本**: 0.9.2
- **描述**: 一个用于生成随机数的库。
- **特性**: 无

## rocket
- **版本**: 0.5.1
- **描述**: 一个用于构建快速、安全的 Web 应用程序的框架。
- **特性**: `json`

## serde
- **版本**: 1.0.219
- **描述**: 一个用于高效、通用地序列化和反序列化 Rust 数据结构的框架。
- **特性**: `derive`

## serde_json
- **版本**: 1.0.141
- **描述**: 为 Serde 框架提供 JSON 支持。
- **特性**: 无

## serde_yaml
- **版本**: 0.9.34
- **描述**: 为 Serde 框架提供 YAML 支持。
- **特性**: 无

## sha2
- **版本**: 0.10.9
- **描述**: 提供 SHA-2 系列哈希函数的纯 Rust 实现。
- **特性**: 无

## sqlx
- **版本**: 0.8.6
- **描述**: 一个异步、安全的 Rust SQL 工具包。
- **特性**: `runtime-tokio-rustls`, `mysql`, `chrono`, `uuid`

## thiserror
- **版本**: 2.0.12
- **描述**: 一个提供方便的派生宏来消除实现 `std::error::Error` 的样板代码的库。
- **特性**: 无

## tokio
- **版本**: 1.46.1
- **描述**: 一个用于编写异步、事件驱动的网络应用程序的运行时。
- **特性**: `full`

## tracing
- **版本**: 0.1.41
- **描述**: 一个用于检测 Rust 程序以收集结构化、事件驱动的诊断信息的框架。
- **特性**: 无

## tracing-subscriber
- **版本**: 0.3.19
- **描述**: `tracing` 生态系统的一部分，用于处理和输出跟踪数据。
- **特性**: 无

## uuid
- **版本**: 1.17.0
- **描述**: 一个用于生成和操作 UUID（通用唯一标识符）的库。
- **特性**: `v4`, `serde`
## redis
- **版本**: 0.25.4
- **描述**: 一个用于与 Redis 数据库交互的 Rust 客户端库。
- **特性**: `tokio-comp`